import { Router, type Request<PERSON><PERSON><PERSON> } from "express";
import { createQueuedProxyMiddleware } from "../middleware/request/proxy-middleware-factory";
import { addKey, finalizeBody } from "../middleware/request";
import { createPreprocessorMiddleware } from "../middleware/request";
import { ipLimiter } from "../middleware/request/rate-limit";
import type { ProxyResHandlerWithBody } from "../middleware/response";
import { modelsCache } from "@/shared/cache";
import { keyPool } from "@/shared/key-management";
import type { XAIKey } from "@/shared/key-management";
import { isAllowedModel } from "@/shared/models";

export const getModelsResponse = () => {
  if (keyPool.getKeyProvider("xai").available() === 0) {
    return [];
  }

  const keys = keyPool.getKeyProvider("xai").list() as XAIKey[];
  const modelIds = Array.from(new Set(keys.map((k) => k.modelIds).flat()));

  const models = modelIds
    .filter((id) => isAllowedModel(id))
    .map((id) => ({
      id,
      object: "model",
      created: new Date().getTime(),
      owned_by: "xai",
      permission: [],
      root: "xai",
      parent: null,
    }));

  return models;
};

const handleModelRequest: RequestHandler = async (_req, res) => {
  const cache = await modelsCache.get<ReturnType<typeof getModelsResponse>>("xai");

  if (cache) {
    res.setHeader("Cache-State", "HIT");
    return res.status(200).json({ object: "list", data: cache });
  }

  const models = getModelsResponse();
  await modelsCache.set("xai", models);

  res.setHeader("Cache-State", "MISS");
  return res.status(200).json({ object: "list", data: models });
};

const xaiResponseHandler: ProxyResHandlerWithBody = async (_proxyRes, req, res, body) => {
  if (typeof body !== "object") {
    throw new Error("Expected body to be an object");
  }

  let newBody = body;
  res.status(200).json({ ...newBody, proxy: body.proxy });
};

const xaiProxy = createQueuedProxyMiddleware({
  mutations: [addKey, finalizeBody],
  target: "https://api.x.ai",
  blockingResponseHandler: xaiResponseHandler,
});

const xaiRouter = Router();
xaiRouter.get("/v1/models", handleModelRequest);

// General chat completion endpoint
xaiRouter.post(
  "/v1/chat/completions",
  ipLimiter,
  createPreprocessorMiddleware({ inApi: "openai", outApi: "openai", service: "xai" }),
  xaiProxy
);

// Redirect browser requests to the homepage.
xaiRouter.get("*", (req, res, next) => {
  const isBrowser = req.headers["user-agent"]?.includes("Mozilla");
  if (isBrowser) res.redirect("/");
  else next();
});

export const xai = xaiRouter;
