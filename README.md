# AI Services Reverse Proxy

---

Reverse proxy server for various AI services APIs.

### Table of Contents

<!-- TOC -->

- [AI Services Reverse Proxy](#ai-services-reverse-proxy)
  - [Table of Contents](#table-of-contents)
  - [License & Disclaimer](#license--disclaimer)
  - [What is this?](#what-is-this)
  - [Features](#features)
  - [Requirements](#requirements)
  - [Usage Instructions](#usage-instructions)
  - [Proxy Configuration](#proxy-configuration)
    - [Personal Use (single-user)](#personal-use-single-user)
      - [Updating](#updating)
      - [Local Development](#local-development)
    - [Self-hosting](#self-hosting)

<!-- TOC -->

## License & Disclaimer

This project is licensed under the MIT License. See the [LICENSE](./LICENSE) file for details.

**IMPORTANT DISCLAIMER:**

- This software is intended for legitimate use only, with properly obtained API keys and in compliance with all applicable terms of service.
- Using this software with stolen, leaked, or otherwise unauthorized API keys is strictly prohibited and may be illegal.
- The authors and contributors:
  - Do not endorse or support the use of unauthorized API keys
  - Are not responsible for any misuse of the software
  - Are not liable for any consequences of using the software with unauthorized credentials
- Users are solely responsible for ensuring they have proper authorization for any API keys they use.
- Any use of this software implies acceptance of these terms.

## What is this?

This project allows you to run a reverse proxy server for various AI services APIs.

## Features

- [x] Support for multiple APIs
  - [x] [OpenAI](https://openai.com/)
  - [x] [Anthropic](https://www.anthropic.com/)
  - [x] [Google MakerSuite/Gemini API](https://ai.google.dev/)
  - [x] [DeepSeek](https://deepseek.com/)
- [x] Translation from OpenAI-formatted prompts to any other API, including streaming responses
- [x] Multiple API keys with rotation and rate limit handling
- [x] Basic user management
  - [x] Simple role-based permissions
  - [x] Per-model token quotas
  - [x] Temporary user accounts
- [x] Event audit logging

## Usage Instructions

If you'd like to run your own instance of this server, you'll need to deploy it somewhere and configure it with your API keys. A few easy options are provided below, though you can also deploy it to any other service you'd like if you know what you're doing and the service supports Node.js and Bun.

### Requirements

- [Bun](https://bun.sh/)

### Proxy Configuration

The proxy can be configured with `proxy.config.ts` in the root of the project. Here is an example:

```ts
import { defineConfig } from "./src/config";

export default defineConfig({
  gatekeeper: "user_token",
  gatekeeperStore: "sqlite",

  showTokenCosts: true,
  publicJsonInfo: true,
  hashIp: true,
  checkKeys: true,
  eventLogging: true,

  allowNicknameChanges: true,
  maxIpsAutoBan: false,
  maxIpsPerUser: 0,

  disallowedModels: [
    "claude-3-opus",
    "gpt-4.5-preview",
    /4o(-mini)?-(transcribe|realtime|audio|search|tts)/,
    "o1-pro",
  ],

  defaultGlobalRateLimit: 20,
  defaultGlobalMaxContext: 0,
  defaultGlobalMaxOutput: 1000,

  modelFamilySettings: {
    // This overrides the default rate limit for the ChatGPT 4o model family
    "chatgpt-4o": {
      rateLimit: 10,
      maxContext: 32000,
      maxOutput: 4096,
    },
  },

  // You can pass your own environment variables here or it will auto read from .env (see .env to see the default keys)
  proxyKey: process.env.CUSTOM_PROXY_KEY, // Default: process.env.PROXY_KEY
  adminKey: process.env.ADMIN_KEY,
  serviceInfoPassword: process.env.SERVICE_INFO_PASSWORD,

  sqliteDataPath: "./data/database.sqlite",
  redisUrl: process.env.REDIS_URL,

  allowOpenAIToolUsage: true,
  allowedVisionServices: ["openai", "anthropic", "google-ai", "deepseek"],
});
```

Refer to the [config.ts](./src/config.ts) file for more information on the available and default configuration options.

### Personal Use (single-user)

If you just want to run the proxy server to use yourself without hosting it for others:

1. Install [Bun](https://bun.sh/)
2. Clone this repository
3. Create a `.env` file in the root of the project and add your API keys. See the [.env.example](./.env.example) file for an example. Then create `proxy.config.ts` to configure the proxy. See the [Proxy Configuration](#proxy-configuration) section for more information.
4. Install dependencies with `bun install`
5. Run `bun start`

#### Updating

You must re-run `bun install` and `bun start` whenever you pull new changes from the repository.

#### Local Development

Use `bun dev` to run the proxy in development mode with watch mode enabled. Use `bun type-check` to run the type checker across the project.

#### Roadmap

- [x] Rework on Admin Dashboard
  - [x] Login
  - [x] Dashboard
  - [x] List Users
    - [x] View User
  - [x] Create User
  - [x] Import Users
  - [x] Export Users
  - [x] Key Manager
    - [x] View Key
- [x] Rework user lookup page
- [ ] Rework service info page
- [ ] Add a forbidden model on user level
- [ ] Make key data presist across restart
- [ ] Add Grok API support
- [ ] Image generation support (DALL-E and GPT-Image)
- [ ] Update user meta and disallowed models

### Self-hosting

[See here for instructions on how to self-host the application on your own VPS or local machine and expose it to the internet for others to use.](./docs/self-hosting.md)

**Ensure you set the `TRUSTED_PROXIES` environment variable according to your deployment.** Refer to [.env.example](./.env.example) and [config.ts](./src/config.ts) for more information.
