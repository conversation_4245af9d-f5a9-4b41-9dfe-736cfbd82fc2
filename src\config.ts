import "dotenv/config";

import chokidar from "chokidar";
import crypto from "crypto";
import fs from "fs";
import diff from "microdiff";
import path from "path";
import pino from "pino";
import pretty from "pino-pretty";

import { z } from "zod/v4";

import { LLM_SERVICES, MODEL_FAMILIES } from "./shared/models";
import { ObjectTyped, normalizeForDiff } from "./shared/utils";

const startupLogger = pino(
  { level: "debug", base: { pid: process.pid } },
  pino.multistream({
    level: "debug",
    stream: pretty({
      singleLine: true,
      messageFormat: "{if module}\x1b[90m[{module}] \x1b[39m{end}{msg}",
      ignore: "module",
      colorize: true,
    }),
  })
).child({ module: "startup" });
const isDev = process.env.NODE_ENV !== "production";

export const DATA_DIR = path.join(__dirname, "..", "data");
export const USER_ASSETS_DIR = path.join(DATA_DIR, "user-files");

function keysTransform(keys?: string | null) {
  if (!keys) return [];
  return [...new Set(keys.split(",").map((k) => k.trim()))];
}

const configSchema = z.object({
  /**
   * The title of the server, displayed on the info page.
   * If not set, the title will be default to `AI-Reverse-Proxy`.
   */
  serverTitle: z.string().optional().default("AI-Reverse-Proxy"),
  /**
   * The port the proxy server will listen on.
   * If not set, the default is `7860`.
   */
  port: z.coerce.number().default(7860),
  /**
   * The network interface the proxy server will listen on.
   * If not set, the default is `0.0.0.0`.
   */
  bindAddress: z.string().default("0.0.0.0"),

  /**
   * The API keys to use for each service. You can pass your own environment variables here
   * or it will auto read from .env
   *
   * You can set the key to `null` to disable the service. Even if the environment variable is set,
   * it will be ignored if the key is set to `null` here.
   *
   * If not set, the default will be read from `process.env`.
   * - Openai: `process.env.OPENAI_KEYS`
   * - Anthropic: `process.env.ANTHROPIC_KEYS`
   * - Google: `process.env.GOOGLE_AI_KEYS`
   * - DeepSeek: `process.env.DEEPSEEK_KEYS`
   * - XAI: `process.env.XAI_KEYS`
   */
  keys: z
    .object({
      /**
       * The OpenAI API keys to use. Pass `null` to disable OpenAI support.
       * If not set, the default is `process.env.OPENAI_KEYS`.
       */
      openai: z
        .string()
        .nullish()
        .default(process.env.OPENAI_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The Anthropic API keys to use. Pass `null` to disable Anthropic support.
       * If not set, the default is `process.env.ANTHROPIC_KEYS`.
       */
      anthropic: z
        .string()
        .nullish()
        .default(process.env.ANTHROPIC_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The Google API keys to use. Pass `null` to disable Google support.
       * If not set, the default is `process.env.GOOGLE_AI_KEYS`.
       */
      google: z
        .string()
        .nullish()
        .default(process.env.GOOGLE_AI_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The DeepSeek API keys to use. Pass `null` to disable DeepSeek support.
       * If not set, the default is `process.env.DEEPSEEK_KEYS`.
       */
      deepseek: z
        .string()
        .nullish()
        .default(process.env.DEEPSEEK_KEYS ?? "")
        .transform(keysTransform),

      /**
       * The XAI API keys to use. Pass `null` to disable XAI support.
       * If not set, the default is `process.env.XAI_KEYS`.
       */
      xai: z
        .string()
        .nullish()
        .default(process.env.XAI_KEYS ?? "")
        .transform(keysTransform),
    })
    .prefault({}),

  /**
   * The proxy key to require for requests.
   * Only applicable if the user management mode is set to `proxy_key`, and required if so.
   *
   * If not set, the default is `process.env.PROXY_KEY`.
   */
  proxyKey: z
    .string()
    .optional()
    .overwrite((v) => v ?? process.env.PROXY_KEY),

  /**
   * The admin key used to access the /admin API or UI.
   * Required if the user management mode is set to `user_token`.
   *
   * If not set, the default is `process.env.ADMIN_KEY`.
   */
  adminKey: z
    .string()
    .optional()
    .overwrite((v) => v ?? process.env.ADMIN_KEY),

  /**
   * The password required to view the service info/status page.
   * If not set, the info page will be publicly accessible.
   *
   * If not set, the default is `process.env.SERVICE_INFO_PASSWORD`.
   */
  serviceInfoPassword: z
    .string()
    .optional()
    .overwrite((v) => v ?? process.env.SERVICE_INFO_PASSWORD),

  /**
   * The signing key used to sign cookies and tokens.
   * If not set, a random key will be generated on startup.
   * It's recommended to set this explicitly.
   *
   * If not set, the default is `process.env.COOKIE_SECRET` or `process.env.SIGNING_KEY`, `process.env.SIGNING_KEY` take precedence.
   */
  signingKey: z
    .string()
    .optional()
    .overwrite((v) => v ?? process.env.COOKIE_SECRET ?? process.env.SIGNING_KEY),

  /**
   * URL of the Redis server if using the Redis store. The URL should be in the format `redis://username:password@host:port/db-number`.
   * If not set, the Redis store will be disabled and the memory store will be used.
   */
  redisUrl: z
    .string()
    .optional()
    .overwrite((v) => v ?? process.env.REDIS_URL),

  /**
   * Which user management mode to use.
   * - `none`: No user management. Proxy is open to all requests with basic abuse protection.
   * - `proxy_key`: A specific proxy key must be provided in the Authorization header to use the proxy.
   * - `user_token`: Users must be created via by admins and provide their personal access token in the Authorization header to use the proxy. Configure this function and add users via the admin API or UI.
   */
  gatekeeper: z.enum(["none", "proxy_key", "user_token"]).default("none"),

  /**
   * Persistence layer to use for user management.
   * - `memory`: Users are stored in memory and are lost on restart (default)
   * - `sqlite`: Users are stored in a SQLite database.
   * - `turso`: Users are stored in a Turso database. https://turso.tech/
   */
  gatekeeperStore: z.enum(["memory", "sqlite", "turso"]).default("memory"),

  /**
   * Path to the SQLite database file for storing data such as event logs. By
   * default, the database will be stored at `data/database.sqlite`.
   *
   * Ensure target is writable by the server process, and be careful not to
   * select a path that is served publicly. The default path is safe.
   */
  sqliteDataPath: z.string().default(path.join(DATA_DIR, "database.sqlite")),

  /**
   * Maximum number of IPs allowed per user token.
   * Users with the manually-assigned `special` role are exempt from this limit.
   * - Defaults to 0, which means that users are not IP-limited.
   */
  maxIpsPerUser: z.coerce.number().int().min(0).default(0),
  /**
   * Whether a user token should be automatically disabled if it exceeds the `maxIpsPerUser` limit, or if only connections from new IPs are be rejected.
   */
  maxIpsAutoBan: z.boolean().default(false),

  /**
   * Whether to log events, such as generated completions, to the database.
   * Currently logged events include:
   *
   * - `chat-completion`: When a chat completion request is made.
   * - `new-ip`: When a new IP address is added to a user token.
   * - `user-action`: When a user action is performed, such as changing their nickname.
   */
  eventLogging: z.boolean().default(false),

  /**
   * Whether to publicly show total token costs on the info page.
   */
  showTokenCosts: z.boolean().default(false),

  /**
   * Whether to publicly show JSON info on the info page.
   */
  publicJsonInfo: z.boolean().default(false),

  /**
   * Whether to hash IP addresses before storing them in the database.
   * This is useful if you want to store IP addresses but don't want to expose them.
   */
  hashIp: z.boolean().default(false),

  /**
   * The message to display when a user is unauthorized.
   * Defaults to "Unauthorized access.".
   */
  unauthorizedMessage: z.string().default("Unauthorized access."),

  logging: z
    .object({
      /**
       * The verbosity level of diagnostic logging.
       * If not set, the default is `info`.
       */
      level: z.enum(["trace", "debug", "info", "warn", "error"]).default("info"),

      /**
       * Where to output logs.
       * - `console`: Log only to console
       * - `file`: Log only to files
       * - `both`: Log to both console and files (default)
       */
      output: z.enum(["console", "file", "both"]).default("both"),

      /**
       * Directory path for log files when file logging is enabled.
       * Log files will be created with date-based rotation:
       * - `default-YYYY-MM-DD.log` for info, warn, debug, trace logs
       * - `error-YYYY-MM-DD.log` for error and fatal logs
       *
       * If not set, the default is `./data/logging`.
       */
      directory: z.string().default(path.join(DATA_DIR, "logging")),
    })
    .prefault({}),

  /**
   * Whether to use a more minimal public Service Info page with static content.
   * Disables all stats pertaining to traffic, prompt/token usage, and queues.
   *
   * The full info page will appear if you have signed in as an admin using the
   * configured ADMIN_KEY and go to /admin/service-info.
   **/
  staticServiceInfo: z.boolean().default(false),

  /**
   * Whether to periodically check keys for usage and validity.
   */
  checkKeys: z.boolean().default(!isDev),

  /**
   * Whether to allow users to change their own nicknames via the UI.
   */
  allowNicknameChanges: z.boolean().default(true),

  /**
   * If true, cookies will be set without the `Secure` attribute, allowing
   * the admin UI to used over HTTP.
   */
  useInsecureCookies: z.boolean().default(isDev),

  /**
   * Trusted proxy hops. If you are deploying the server behind a reverse proxy
   * (Nginx, Cloudflare Tunnel, AWS WAF, etc.) the IP address of incoming
   * requests will be the IP address of the proxy, not the actual user.
   *
   * Depending on your hosting configuration, there may be multiple proxies/load
   * balancers between your server and the user. Each one will append the
   * incoming IP address to the `X-Forwarded-For` header. The user's real IP
   * address will be the first one in the list, assuming the header has not been
   * tampered with. Setting this value correctly ensures that the server doesn't
   * trust values in `X-Forwarded-For` not added by trusted proxies.
   *
   * In order for the server to determine the user's real IP address, you need
   * to tell it how many proxies are between the user and the server so it can
   * select the correct IP address from the `X-Forwarded-For` header.
   *
   * ! WARNING: If you set it incorrectly, the proxy will either record the
   * wrong IP address, or it will be possible for users to spoof their IP
   * addresses and bypass rate limiting. Check the request logs to see what
   * incoming X-Forwarded-For values look like.
   *
   * Examples:
   *  - X-Forwarded-For: "********, *********, ********" => trustedProxies: 3
   *  - X-Forwarded-For: "********" => trustedProxies: 1
   *  - no X-Forwarded-For header => trustedProxies: 0 (the actual IP of the incoming request will be used)
   *
   * As of 2024/01/08:
   * For HuggingFace or Cloudflare Tunnel, use 1.
   * For Render, use 3.
   * For deployments not behind a load balancer, use 0.
   *
   * You should double check against your actual request logs to be sure.
   * Defaults to 1, as most deployments are on HuggingFace or Cloudflare Tunnel.
   */
  trustedProxies: z.number().int().min(0).default(1),

  /**
   * Whether to allow tool usage. The proxy doesn't impelment any
   * support for tools/function calling but can pass requests and responses as is.
   *
   * Defaults to no services, meaning tool usage is disabled.
   *
   * Available services are: `openai`,`anthropic`,`google-ai`, `deepseek`
   */
  allowToolUsage: z.array(z.enum(LLM_SERVICES)).default([]),

  /**
   * Which services will accept prompts containing images, for use with
   * multimodal models. Users with `special` role are exempt from this
   * restriction.
   *
   * Do not enable this feature for untrusted users, as malicious users could
   * send images which violate your provider's terms of service or local laws.
   *
   * Defaults to no services, meaning image prompts are disabled.
   *
   * Available services are: `openai`,`anthropic`,`google-ai`, `deepseek`, `xai`
   * - For `deepseek`, vision is not supported so this setting has no effect.
   */
  allowedVisionServices: z.array(z.enum(LLM_SERVICES)).default([]),

  /**
   * Allows overriding the default proxy endpoint route. Defaults to /proxy.
   * A leading slash is required.
   */
  proxyEndpointRoute: z.string().default("/proxy"),

  /**
   * If set, only requests from these IP addresses will be permitted to use the
   * admin API and UI. Provide a comma-separated list of IP addresses or CIDR
   * ranges. If not set, the admin API and UI will be open to all requests.
   */
  adminWhitelist: z.array(z.string()).default(["0.0.0.0/0", "::/0"]),

  /**
   * If set, requests from these IP addresses will be blocked from using the
   * application. Provide a comma-separated list of IP addresses or CIDR ranges.
   * If not set, no IP addresses will be blocked.
   *
   * Takes precedence over the adminWhitelist.
   */
  ipBlacklist: z.array(z.string()).default([]),

  /**
   * The factor by which the tokens used in a request will be multiplied to determine the punishment time.
   * If not set, the default is 0.0.
   */
  tokensPunishmentFactor: z.number().min(0).default(0.0),

  /**
   * Default rate limit for all models.
   * If not set, the default is 10.
   */
  defaultGlobalRateLimit: z.number().int().min(0).default(10),

  /**
   * Default context limit for all models.
   * If not set, the default is 0 (no limit).
   */
  defaultGlobalMaxContext: z
    .number()
    .int()
    .min(0)
    .default(0)
    .transform((v) => (v === 0 ? Number.MAX_SAFE_INTEGER : v)),

  /**
   * Default output limit for all models.
   * If not set, the default is 0 (no limit).
   */
  defaultGlobalMaxOutput: z
    .number()
    .int()
    .min(0)
    .default(0)
    .transform((v) => (v === 0 ? Number.MAX_SAFE_INTEGER : v)),

  /**
   * Model-specific rate limits, context limits, and output limits.
   * If not set, the default to the global limits.
   */
  modelFamilySettings: z
    .partialRecord(
      z.enum(MODEL_FAMILIES),
      z.object({
        rateLimit: z.number().min(0).default(0).optional(),
        maxContext: z.number().min(0).default(0).optional(),
        maxOutput: z.number().min(0).default(0).optional(),
      })
    )
    .default({})
    .transform((data) => new Map(ObjectTyped.entries(data))),

  /**
   * List of models that are not allowed to be used.
   * Support regex or partial match.
   *
   * Default disallow model: `o1-pro`, `claude-3-opus`, `gpt-4.5-preview`
   */
  disallowedModels: z
    .array(z.union([z.string(), z.instanceof(RegExp)]))
    .default(["o1-pro", "claude-3-opus", "gpt-4.5-preview"]),

  block: z
    .object({
      /**
       * Comma-separated list of origins to block. Requests matching any of these
       * origins or referers will be rejected.
       *
       * - Partial matches are allowed, so `reddit` will match `www.reddit.com`.
       * - Include only the hostname, not the protocol or path, e.g:
       *  `reddit.com,9gag.com,gaiaonline.com`
       */
      origins: z.array(z.string()).optional(),

      /** Message to return when rejecting requests from blocked origins. */
      message: z.string().optional().default("Access denied."),

      /**
       * Destination URL to redirect blocked requests to, for non-JSON requests.
       */
      redirect: z.string().optional(),
    })
    .optional(),

  /**
   * Configuration for HTTP requests made by the proxy to other servers, such
   * as when checking keys or forwarding users' requests to external services.
   * If not set, all requests will be made using the default agent.
   *
   * If set, the proxy may make requests to other servers using the specified
   * settings. This is useful if you wish to route users' requests through
   * another proxy or VPN, or if you have multiple network interfaces and want
   * to use a specific one for outgoing requests.
   */
  httpAgent: z
    .object({
      /**
       * The name of the network interface to use. The first external IPv4 address
       * belonging to this interface will be used for outgoing requests.
       */
      interface: z.string().optional(),

      /**
       * The URL of a proxy server to use. Supports SOCKS4, SOCKS5, HTTP, and
       * HTTPS. If not set, the proxy will be made using the default agent.
       * - SOCKS4: `socks4://some-socks-proxy.com:9050`
       * - SOCKS5: `socks5://username:<EMAIL>:9050`
       * - HTTP: `http://proxy-server-over-tcp.com:3128`
       * - HTTPS: `https://proxy-server-over-tls.com:3129`
       *
       * **Note:** If your proxy server issues a certificate, you may need to set
       * `NODE_EXTRA_CA_CERTS` to the path to your certificate, otherwise this
       * application will reject TLS connections.
       */
      proxyUrl: z.string().optional(),
    })
    .optional(),

  /**
   * Enable experimental features. This may contain breaking changes.
   * Use at your own risk.
   *
   * List of experimental features:
   */
  experimentals: z.object({}).optional(),
});

type Config = z.infer<typeof configSchema>;

export function defineConfig(config: z.input<typeof configSchema>) {
  const result = configSchema.safeParse(config);

  if (!result.success) {
    throw Error("Config validation failed: " + result.error.message, { cause: result.error });
  }

  return result.data;
}

function loadCustomConfig() {
  const defaultConfig = configSchema.parse({});
  const customConfigPath = path.join(__dirname, "..", "proxy.config.ts");

  if (!fs.existsSync(customConfigPath)) return defaultConfig;
  const customConfig = require(customConfigPath).default as ReturnType<typeof defineConfig>;

  return { ...defaultConfig, ...customConfig };
}

/**
 * To change configs, create a file called `proxy.config.ts` in the root directory.
 *
 * See `.env.example` and `README.md` for more information.
 */
export const config: Config = loadCustomConfig();

const isServer = process.argv[1].includes("server.ts");

if (isServer) {
  const watcher = chokidar.watch("./proxy.config.ts", {
    persistent: true,
    ignoreInitial: true,
    awaitWriteFinish: true,
  });

  startupLogger.info("Watching for `proxy.config.ts` changes...");

  watcher.on("error", (error) => {
    startupLogger.error(error, "Error watching file:");
  });

  watcher.on("change", (path) => {
    delete require.cache[require.resolve(path)];
    const newConfig = loadCustomConfig();

    const patches = diff(normalizeForDiff(config), normalizeForDiff(newConfig));
    const topKeys = new Set(patches.map((p) => String(p.path[0]) as keyof Config));

    if (topKeys.has("keys")) {
      startupLogger.warn(
        "Config changed, but updating service api keys is not supported. Please use Admin UI to update keys."
      );
      topKeys.delete("keys");
    }

    const dangerous = DANGEROUS_KEYS.filter((k) => topKeys.has(k));
    if (dangerous.length) {
      startupLogger.warn(
        { changedKeys: Array.from(topKeys), dangerousChanges: dangerous },
        "Dangerous config key(s) changed - please restart the server manually to apply these changes and avoid undefined behavior."
      );

      if (dangerous.length === topKeys.size) return;

      for (const key of dangerous) topKeys.delete(key);
      startupLogger.info(
        { changedKeys: Array.from(topKeys), dangerousChanges: dangerous },
        "Non-dangerous config key(s) changed. Removed dangerous key changes, continued applying changes..."
      );
    }

    if (!topKeys.size) {
      startupLogger.info("No config changes detected.");
      return;
    }

    startupLogger.info(
      { changed: patches.map((p) => p.path.join(".")) },
      "Applying config changes..."
    );

    Object.assign(config, newConfig);
    startupLogger.info("Config changes applied.");
  });
}

function generateSigningKey() {
  const secrets = [
    config.adminKey,
    config.keys.openai,
    config.keys.anthropic,
    config.keys.google,
    config.keys.deepseek,
  ];

  if (secrets.filter((s) => s).length === 0) {
    startupLogger.warn(
      "No SIGNING_KEY or secrets are set. All sessions, cookies, and proofs of work will be invalidated on restart."
    );
    return crypto.randomBytes(32).toString("hex");
  }

  startupLogger.info("No SIGNING_KEY set; one will be generated from secrets.");
  startupLogger.info(
    "It's recommended to set SIGNING_KEY explicitly to ensure users' sessions and cookies always persist across restarts."
  );
  const seed = secrets.map((s) => (Array.isArray(s) ? s.join("") : s) || "n/a").join("");
  return crypto.createHash("sha256").update(seed).digest("hex");
}

export const SECRET_SIGNING_KEY = config.signingKey ?? generateSigningKey();

export async function assertConfigIsValid() {
  if (config.gatekeeper === "user_token" && !config.adminKey) {
    throw new Error(
      "Missing `adminKey` config. Required for `user_token` gatekeeper mode. Please set it in `proxy.config.ts` or `ADMIN_KEY` in `.env`."
    );
  }

  if (config.gatekeeper === "proxy_key" && !config.proxyKey) {
    throw new Error(
      "Missing `proxyKey` config. Required for `proxy_key` gatekeeper mode. Please set it in `proxy.config.ts` or `PROXY_KEY` in `.env`."
    );
  }

  if (config.httpAgent?.interface && config.httpAgent?.proxyUrl) {
    throw new Error("Cannot set both `httpAgent.interface` and `httpAgent.proxyUrl`.");
  }

  // Ensure forks which add new secret-like config keys don't unwittingly expose them to users.
  for (const key of ObjectTyped.keys(config)) {
    const maybeSensitive = ["key", "credentials", "secret", "password", "url"].some(
      (sensitive) => key.toLowerCase().includes(sensitive) && !["checkKeys"].includes(key)
    );

    const secured = new Set<string>([...SENSITIVE_KEYS, ...OMITTED_KEYS]);
    if (maybeSensitive && !secured.has(key))
      throw new Error(
        `Config key "${key}" may be sensitive but is exposed. Add it to SENSITIVE_KEYS or OMITTED_KEYS.`
      );
  }
}

/**
 * Config keys that are masked on the info page, but not hidden as their
 * presence may be relevant to the user due to privacy implications.
 */
export const SENSITIVE_KEYS = [] satisfies (keyof Config)[];
type SensitiveKeys = (typeof SENSITIVE_KEYS)[number];

/**
 * Config keys that are not suppose to changes after the server starts.
 * If changed, could cause unexpected behavior or errors.
 */
export const DANGEROUS_KEYS = [
  "port",
  "keys",
  "logging",
  "checkKeys",
  "bindAddress",
  "trustedProxies",
  "sqliteDataPath",
  "proxyEndpointRoute",
  "signingKey",
  "redisUrl",
  "httpAgent",
  "gatekeeper",
  "gatekeeperStore",
] satisfies (keyof Config)[];
type DangerousKeys = (typeof DANGEROUS_KEYS)[number];

/**
 * Config keys that are not displayed on the info page at all, generally because
 * they are not relevant to the user or can be inferred from other config.
 */
export const OMITTED_KEYS = [
  "port",
  "logging",
  "keys",
  "proxyKey",
  "adminKey",
  "serviceInfoPassword",
  "showTokenCosts",
  "sqliteDataPath",
  "eventLogging",
  "gatekeeperStore",
  "maxIpsPerUser",
  "block",
  "httpAgent",
  "allowNicknameChanges",
  "useInsecureCookies",
  "staticServiceInfo",
  "checkKeys",
  "redisUrl",
  "publicJsonInfo",
  "unauthorizedMessage",
  "maxIpsAutoBan",
  "trustedProxies",
  "bindAddress",
  "proxyEndpointRoute",
  "adminWhitelist",
  "ipBlacklist",
  "disallowedModels",
  "serverTitle",
  "signingKey",
] satisfies (keyof Config)[];
type OmitKeys = (typeof OMITTED_KEYS)[number];

type Printable<T> = {
  [P in keyof T as Exclude<P, OmitKeys>]: T[P] extends object ? Printable<T[P]> : string;
};
type PublicConfig = Printable<Config>;

export function listConfig() {
  const data = JSON.stringify(config, function (key, value) {
    if (OMITTED_KEYS.includes(key as OmitKeys)) return undefined;
    if (SENSITIVE_KEYS.includes(key as SensitiveKeys)) return "********";

    if (value instanceof Map) return Object.fromEntries(value);
    if (value instanceof Set) return Array.from(value).join(",");
    if (Array.isArray(value)) return value.join(",");

    return value;
  });

  return JSON.parse(data) as PublicConfig;
}
