import crypto from "crypto";

import { config } from "@/config";
import { logger } from "@/logger";

import { PaymentRequiredError } from "@/shared/errors";
import { getXAIModelFamily, type XAIModelFamily } from "@/shared/models";

import { getTokenCostUsd } from "@/shared/stats";
import {
  createGenericGetLockoutPeriod,
  defaultUsages,
  type Key,
  type KeyProvider,
  type Usages,
} from "../index";
import { prioritizeKeys } from "../prioritize-keys";
import { XAIKeyChecker } from "./checker";

type XAIKeyUsage = {
  [K in XAIModelFamily as `${K}-usages`]: Usages;
};

export interface XAIKey extends Key, XAIKeyUsage {
  readonly service: "xai";
  readonly modelFamilies: XAIModelFamily[];

  /** Set when key check returns a non-transient 429. */
  isOverQuota: boolean;
  /**
   * Model snapshots available.
   */
  modelIds: string[];
}

/**
 * Upon being rate limited, a key will be locked out for this many milliseconds
 * while we wait for other concurrent requests to finish.
 */
const RATE_LIMIT_LOCKOUT = 2000;
/**
 * Upon assigning a key, we will wait this many milliseconds before allowing it
 * to be used again. This is to prevent the queue from flooding a key with too
 * many requests while we wait to learn whether previous ones succeeded.
 */
const KEY_REUSE_DELAY = 500;

export class XAIKeyProvider implements KeyProvider<XAIKey> {
  readonly service = "xai";
  private keys: XAIKey[] = [];
  private checker?: XAIKeyChecker;
  private log = logger.child({ module: "key-provider", service: this.service });

  constructor() {
    const keyConfig = config.keys.xai;

    if (keyConfig.length === 0) {
      this.log.warn("XAI_KEYS is not set. XAI API will not be available.");
      return;
    }

    for (const key of keyConfig) this.addKey({ key, isStartup: true });
    this.log.info({ keyCount: this.keys.length }, "Loaded XAI keys.");
  }

  addKey({ key, isStartup = false }: { key: string; isStartup?: boolean }): boolean {
    const hash = `xai-${crypto.createHash("sha256").update(key).digest("hex").slice(0, 8)}`;

    if (!isStartup) {
      const isExist = this.keys.find((k) => k.hash === hash);
      if (isExist) return false;
    }

    const newKey: XAIKey = {
      key,
      hash: hash,
      service: this.service,
      modelFamilies: ["grok"],
      isDisabled: false,
      isOverQuota: false,
      isRevoked: false,
      input: { tokens: 0, cost: 0 },
      output: { tokens: 0, cost: 0 },
      prompts: 0,
      lastUsedAt: 0,
      rateLimitedAt: 0,
      rateLimitedUntil: 0,
      lastCheckedAt: 0,
      addedAt: Date.now(),
      modelIds: [],

      "grok-usages": structuredClone(defaultUsages),
    };
    this.keys.push(newKey);
    return true;
  }

  removeKey(hash: string): boolean {
    const keyIndex = this.keys.findIndex((k) => k.hash === hash);
    if (keyIndex === -1) return false;

    this.keys.splice(keyIndex, 1);
    return true;
  }

  public init() {
    if (config.checkKeys) {
      this.checker = new XAIKeyChecker(this.keys, this.update.bind(this));
      this.checker.start();
    }
  }

  public list() {
    return this.keys.map((k) => Object.freeze({ ...k, key: undefined }));
  }

  public get(rawModel: string) {
    this.log.debug({ model: rawModel }, "Selecting key");

    const availableKeys = this.keys.filter((k) => !k.isDisabled);

    if (availableKeys.length === 0) {
      throw new PaymentRequiredError("No XAI keys available.");
    }

    const keysByPriority = prioritizeKeys(availableKeys);

    const selectedKey = keysByPriority[0];
    selectedKey.lastUsedAt = Date.now();
    this.throttle(selectedKey.hash);
    return { ...selectedKey };
  }

  public disable(key: XAIKey) {
    const keyFromPool = this.keys.find((k) => k.hash === key.hash);
    if (!keyFromPool || keyFromPool.isDisabled) return;
    keyFromPool.isDisabled = true;
    this.log.warn({ key: key.hash }, "Key disabled");
  }

  public update(keyHash: string, update: Partial<XAIKey>) {
    const keyFromPool = this.keys.find((k) => k.hash === keyHash)!;
    Object.assign(keyFromPool, { lastCheckedAt: Date.now(), ...update });
  }

  public available() {
    return this.keys.filter((k) => !k.isDisabled).length;
  }

  public incrementUsage(keyHash: string, model: string, inputTokens: number, outputTokens: number) {
    const key = this.keys.find((k) => k.hash === keyHash);
    if (!key) return;

    key.prompts++;
    key.lastUsedAt = Date.now();

    const family = getXAIModelFamily(model);
    const familyUsages = key[`${family}-usages`];

    familyUsages.tokens += inputTokens + outputTokens;
    familyUsages.requests++;

    key.input.tokens += inputTokens;
    key.output.tokens += outputTokens;

    // XAI pricing is similar to OpenAI - we'll use estimated costs
    // These are placeholder values and should be updated with actual XAI pricing
    const inputCost = getTokenCostUsd(model, inputTokens, "input");
    const outputCost = getTokenCostUsd(model, outputTokens, "output");

    key.input.cost += inputCost;
    key.output.cost += outputCost;
    familyUsages.cost += inputCost + outputCost;
  }

  public getLockoutPeriod = createGenericGetLockoutPeriod(RATE_LIMIT_LOCKOUT);

  public markRateLimited(keyHash: string) {
    this.log.debug({ key: keyHash }, "Key rate limited");
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const now = Date.now();
    key.rateLimitedAt = now;
    key.rateLimitedUntil = now + RATE_LIMIT_LOCKOUT;
  }

  public recheck() {
    if (this.checker) {
      this.checker.scheduleCheck();
    }
  }

  private throttle(keyHash: string) {
    const key = this.keys.find((k) => k.hash === keyHash)!;
    const now = Date.now();
    const delay = KEY_REUSE_DELAY;

    key.rateLimitedUntil = Math.max(key.rateLimitedUntil, now + delay);
  }
}
