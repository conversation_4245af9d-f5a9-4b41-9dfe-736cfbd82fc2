import { logger } from "@/logger";
import { XAI<PERSON>ey } from "./provider";

const log = logger.child({ module: "key-checker", service: "xai" });

type UpdateFn = (keyHash: string, update: Partial<XAIKey>) => void;

export class XAIKeyChecker {
  private keys: XAIKey[];
  private updateKey: UpdateFn;
  private timeout?: NodeJS.Timeout;
  private isRunning = false;

  constructor(keys: XAIKey[], updateKey: UpdateFn) {
    this.keys = keys;
    this.updateKey = updateKey;
  }

  public start() {
    if (this.isRunning) {
      log.warn("Key checker already running");
      return;
    }
    log.info("Starting XAI key checker");
    this.isRunning = true;
    this.scheduleCheck();
  }

  public stop() {
    if (this.timeout) {
      clearTimeout(this.timeout);
    }
    this.isRunning = false;
    log.info("Stopped XAI key checker");
  }

  public scheduleCheck() {
    if (!this.isRunning) return;

    const delay = Math.floor(Math.random() * 5000) + 10000; // 10-15 seconds
    this.timeout = setTimeout(() => this.checkKeys(), delay);
  }

  private async checkKeys() {
    if (!this.isRunning) return;

    const uncheckedKeys = this.keys.filter(
      (key) => !key.isDisabled && Date.now() - key.lastCheckedAt > 60000 // 1 minute
    );

    if (uncheckedKeys.length === 0) {
      this.scheduleCheck();
      return;
    }

    log.debug({ keyCount: uncheckedKeys.length }, "Checking XAI keys");

    for (const key of uncheckedKeys.slice(0, 2)) {
      try {
        await this.checkKey(key);
        await new Promise((resolve) => setTimeout(resolve, 1000)); // Rate limit our checks
      } catch (error) {
        log.error({ key: key.hash, error }, "Error checking XAI key");
      }
    }

    this.scheduleCheck();
  }

  private async checkKey(key: XAIKey) {
    const start = Date.now();
    log.debug({ key: key.hash }, "Checking XAI key");

    try {
      const response = await fetch("https://api.x.ai/v1/models", {
        method: "GET",
        headers: {
          Authorization: `Bearer ${key.key}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        const modelIds = data.data?.map((model: any) => model.id) || [];
        
        this.updateKey(key.hash, {
          modelIds,
          isRevoked: false,
          isOverQuota: false,
          lastCheckedAt: Date.now(),
        });

        log.debug(
          { key: key.hash, models: modelIds.length, elapsed: Date.now() - start },
          "XAI key check successful"
        );
      } else if (response.status === 401) {
        this.updateKey(key.hash, {
          isRevoked: true,
          lastCheckedAt: Date.now(),
        });
        log.warn({ key: key.hash }, "XAI key is revoked");
      } else if (response.status === 429) {
        this.updateKey(key.hash, {
          isOverQuota: true,
          lastCheckedAt: Date.now(),
        });
        log.warn({ key: key.hash }, "XAI key is over quota");
      } else {
        log.warn(
          { key: key.hash, status: response.status },
          "Unexpected response from XAI API"
        );
        this.updateKey(key.hash, {
          lastCheckedAt: Date.now(),
        });
      }
    } catch (error) {
      log.error({ key: key.hash, error }, "Network error checking XAI key");
      this.updateKey(key.hash, {
        lastCheckedAt: Date.now(),
      });
    }
  }
}
