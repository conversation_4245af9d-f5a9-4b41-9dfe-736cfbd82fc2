import type { z } from "zod/v4";

import type { RequestPreprocessor } from "../index";

import type { GoogleAIChatMessage } from "@/shared/api-schemas";
import { countTokens } from "@/shared/tokenization";
import { assertNever } from "@/shared/utils";

import type { AnthropicV1MessagesSchema } from "@/shared/api-schemas/anthropic";
import type { GoogleAIV1GenerateContentSchema } from "@/shared/api-schemas/google-ai";
import type { OpenAIV1ChatCompletionSchema } from "@/shared/api-schemas/openai";

/**
 * Given a request with an already-transformed body, counts the number of
 * tokens and assigns the count to the request.
 */
export const countPromptTokens: RequestPreprocessor = async (req) => {
  const service = req.outboundApi;
  let result;

  switch (service) {
    case "openai": {
      const body = req.body as z.infer<typeof OpenAIV1ChatCompletionSchema>;
      req.outputTokens = body.max_tokens || body.max_completion_tokens!;

      result = await countTokens({ req, prompt: body.messages, service });
      break;
    }

    case "anthropic-chat": {
      const body = req.body as z.infer<typeof AnthropicV1MessagesSchema>;
      req.outputTokens = body.max_tokens;

      let system = body.system ?? "";
      if (Array.isArray(system)) system = system.map((m) => m.text).join("\n");
      const prompt = { system, messages: body.messages };

      result = await countTokens({ req, prompt, service });
      break;
    }

    case "google-ai": {
      const body = req.body as z.infer<typeof GoogleAIV1GenerateContentSchema>;
      req.outputTokens = body.generationConfig.maxOutputTokens;

      const system = body.systemInstruction ?? body.system_instruction ?? [];
      const prompt: GoogleAIChatMessage[] = body.contents.concat(system);

      result = await countTokens({ req, prompt, service });
      break;
    }

    default:
      assertNever(service);
  }

  req.promptTokens = result.token_count;

  req.log.debug({ result: result }, "Counted prompt tokens.");
  req.tokenizerInfo = req.tokenizerInfo ?? {};
  req.tokenizerInfo = { ...req.tokenizerInfo, ...result };
};
