{"name": "ai-reverse-proxy", "version": "1.0.0", "description": "Reverse proxy for the AI services API", "scripts": {"postinstall": "patch-package", "dev": "bun --env-file='' --watch --no-clear-screen src/server.ts", "start": "NODE_ENV=production bun --env-file='' src/server.ts", "start:debug": "NODE_ENV=production bun --env-file='' --inspect src/server.ts", "build:css": "bunx tailwindcss -i ./src/input.css -o ./public/css/output.css --watch", "build:css:prod": "bunx tailwindcss -i ./src/input.css -o ./public/css/output.css --minify", "type-check": "tsc --noEmit", "db:migrate": "drizzle-kit migrate", "db:generate": "drizzle-kit generate", "db:studio": "drizzle-kit studio --verbose", "db:push": "drizzle-kit push --strict --verbose"}, "engines": {"node": ">=22.0.0"}, "bin": {"tailwindcss": "node_modules/@tailwindcss/cli/dist/index.js"}, "author": "", "license": "MIT", "dependencies": {"@anthropic-ai/tokenizer": "^0.0.4", "@libsql/client": "^0.15.9", "axios": "^1.9.0", "cache-manager": "^7.0.0", "chokidar": "^4.0.3", "connect-redis": "^8.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csrf-csrf": "^4.0.3", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "ejs": "^3.1.10", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "http-proxy": "1.18.1", "http-proxy-middleware": "^3.0.5", "ioredis": "^5.6.1", "ipaddr.js": "^2.2.0", "memorystore": "^1.6.7", "microdiff": "^1.5.0", "multer": "^2.0.1", "node-schedule": "^2.1.1", "patch-package": "^8.0.0", "pino": "^9.7.0", "pino-http": "^10.5.0", "proxy-agent": "^6.5.0", "sanitize-html": "^2.17.0", "sharp": "^0.34.2", "showdown": "^2.1.0", "superjson": "^2.2.2", "tiktoken": "^1.0.21", "ulid": "^3.0.1", "uuid": "^11.1.0", "zlib": "^1.0.5", "zod": "^3.25.57"}, "devDependencies": {"@tailwindcss/cli": "^4.1.8", "@types/bun": "^1.2.15", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^4.17.17", "@types/express-session": "^1.18.2", "@types/multer": "^1.4.13", "@types/node": "^24.0.0", "@types/node-schedule": "^2.1.7", "@types/sanitize-html": "^2.16.0", "@types/showdown": "^2.0.6", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.1", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "prettier-plugin-ejs": "^1.0.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4.1.8", "typescript": "^5.8.3"}, "overrides": {"@types/express-serve-static-core": "4.17.33"}, "packageManager": "bun@1.2.15", "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide"]}