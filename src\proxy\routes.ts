import express, { type Request<PERSON><PERSON><PERSON> } from "express";

import { config } from "@/config";
import { modelsCache } from "@/shared/cache";
import { keyPool } from "@/shared/key-management";

import { addV1 } from "./add-v1";
import { checkRisuToken } from "./check-risu-token";
import { gatekeeper } from "./gatekeeper";
import { sendErrorToClient } from "./middleware/response/error-generator";

import { anthropic, getModelsResponse as getClaudeModels } from "./services/anthropic";
import { deepseek, getModelsResponse as getDeepSeekModels } from "./services/deepseek";
import { getModelsResponse as getGoogleModels, googleAI } from "./services/google-ai";
import { generateModelList as generateOpenAIModels, openai } from "./services/openai";

const proxyRouter = express.Router();

// Remove `expect: 100-continue` header from requests due to incompatibility
// with node-http-proxy.
proxyRouter.use((req, _res, next) => {
  if (req.headers.expect) {
    delete req.headers.expect;
  }
  next();
});

// Apply body parsers.
proxyRouter.use(
  express.json({ limit: "100mb" }),
  express.urlencoded({ extended: true, limit: "100mb" })
);

// Apply auth/rate limits.
proxyRouter.use(gatekeeper);
proxyRouter.use(checkRisuToken);

// Initialize request queue metadata.
proxyRouter.use((req, _res, next) => {
  req.startTime = Date.now();
  req.retryCount = 0;
  next();
});

// Proxy endpoints.
proxyRouter.use("/openai", addV1, openai);
proxyRouter.use("/anthropic", addV1, anthropic);
proxyRouter.use("/google-ai", addV1, googleAI);
proxyRouter.use("/deepseek", addV1, deepseek);

type ModelDataType = {
  id: string;
  object: string;
  created: number;
  owned_by: string;
  permission: unknown[];
  root?: string;
  parent: null;
};

/**
 * <AUTHOR>
 * @see {@link https://gitgud.io/Drago}
 * @see {@linkcode https://gitgud.io/Drago/oai-reverse-proxy/-/blob/main/src/proxy/routes.ts}
 *
 * @description Get a list of all available models with prefix.
 */
function getUniveralModelsWithoutPrefix() {
  if (keyPool.available() === 0) return [];
  let modelsList: ModelDataType[] = [];

  modelsList = modelsList
    .concat(getGoogleModels())
    .concat(getClaudeModels())
    .concat(getDeepSeekModels())
    .concat(generateOpenAIModels())
    .map((model) => {
      model.id = model.id.split("/").pop() ?? model.id;
      return model;
    });

  return modelsList.toSorted((a, b) => {
    const ownerCompare = a.owned_by.localeCompare(b.owned_by);
    return ownerCompare !== 0 ? ownerCompare : a.id.localeCompare(b.id);
  });
}

const handleModelsRequest: RequestHandler = async (_req, res) => {
  const cache = await modelsCache.get<ModelDataType[]>("universal");

  if (cache) {
    return res.status(200).header("Cache-State", "HIT").json({ object: "list", data: cache });
  }

  const models = getUniveralModelsWithoutPrefix();
  await modelsCache.set("universal", models);

  return res.status(200).header("Cache-State", "MISS").json({ object: "list", data: models });
};

// Universal endpoint for all available models.
const universalRouter = express.Router();

const handleUniversalRequest: RequestHandler = async (req, res, next) => {
  const model = req.body.model as string;

  // Default to OpenAI if there is no prefix in the model name.
  switch (true) {
    case model.includes("claude"):
      return anthropic(req, res, next);

    case model.includes("gemini"):
      return googleAI(req, res, next);

    case model.includes("deepseek"):
      return deepseek(req, res, next);

    default:
      return openai(req, res, next);
  }
};

/**
 * <AUTHOR>
 * @see {@link https://gitgud.io/Drago}
 * @see {@linkcode https://gitgud.io/Drago/oai-reverse-proxy/-/blob/main/src/proxy/routes.ts}
 *
 * @description Universal router for all available models.
 */
universalRouter.get("/v1/models", handleModelsRequest);
universalRouter.post("/v1beta/models", googleAI);
universalRouter.post("/v1/chat/completions", handleUniversalRequest);
universalRouter.post("/v1/completions", deepseek);
universalRouter.post("/v1/(messages|complete)", anthropic);
universalRouter.post(
  [
    "/:apiVersion(v1alpha|v1beta)/models/:modelId:(generateContent|streamGenerateContent)",
    "/v1beta/openai/chat/completions",
  ],
  addV1,
  googleAI
);

proxyRouter.use("/", addV1, universalRouter);

// Redirect browser requests to the homepage.
proxyRouter.get("*", (req, res, next) => {
  const isBrowser = req.headers["user-agent"]?.includes("Mozilla");

  if (isBrowser) res.redirect("/");
  else next();
});

// Send a fake client error if user specifies an invalid proxy endpoint.
proxyRouter.use((req, res) => {
  sendErrorToClient({
    req,
    res,
    options: {
      title: "Proxy error (HTTP 404 Not Found)",
      message: "The requested proxy endpoint does not exist.",
      model: req.body?.model,
      reqId: req.id,
      format: "unknown",
      obj: {
        proxy_note:
          "Your chat client is using the wrong endpoint. Check the Service Info page for the list of available endpoints.",
        requested_url: req.originalUrl,
      },
    },
  });
});

export { proxyRouter as proxyRouter };
