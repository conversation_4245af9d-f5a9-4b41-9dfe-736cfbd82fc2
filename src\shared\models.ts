// Don't import any other project files here as this is one of the first modules
// loaded and it will cause circular imports.

import type { Request } from "express";
import { config } from "../config";

/**
 * The service that a model is hosted on. Distinct from `APIFormat` because some
 * services have interoperable APIs (eg Anthropic, OpenAI).
 */
export type LLMService = "openai" | "anthropic" | "google-ai" | "deepseek" | "xai";

export type OpenAIModelFamily =
  | "turbo"
  | "gpt4"
  | "gpt4-turbo"
  | "gpt4.5"
  | "gpt4.1"
  | "gpt4.1-mini"
  | "gpt4.1-nano"
  | "gpt4o"
  | "gpt4o-mini"
  | "chatgpt-4o"
  | "codex-mini"
  | "o1"
  | "o1-pro"
  | "o1-mini"
  | "o3"
  | "o3-pro"
  | "o3-mini"
  | "o4-mini";

export type DeepSeekModelFamily = "deepseek-chat" | "deepseek-reasoner";
export type AnthropicModelFamily = "claude" | "claude-opus";
export type GoogleAIModelFamily = "gemini-flash-lite" | "gemini-flash" | "gemini-pro";
export type XAIModelFamily = "grok";

export type ModelFamily =
  | OpenAIModelFamily
  | AnthropicModelFamily
  | GoogleAIModelFamily
  | DeepSeekModelFamily
  | XAIModelFamily;

export const MODEL_FAMILIES = (<A extends readonly ModelFamily[]>(
  arr: A & ([ModelFamily] extends [A[number]] ? unknown : never)
) => arr)([
  // OpenAI
  "turbo",
  "gpt4",
  "gpt4-turbo",
  "gpt4.1",
  "gpt4.5",
  "gpt4.1-mini",
  "gpt4.1-nano",
  "gpt4o",
  "gpt4o-mini",
  "chatgpt-4o",
  "codex-mini",
  "o1",
  "o1-pro",
  "o1-mini",
  "o3",
  "o3-pro",
  "o3-mini",
  "o4-mini",

  // Anthropic
  "claude",
  "claude-opus",

  // Google
  "gemini-flash-lite",
  "gemini-flash",
  "gemini-pro",

  // Deepseek
  "deepseek-chat",
  "deepseek-reasoner",

  // XAI
  "grok",
] as const);

export const LLM_SERVICES = (<A extends readonly LLMService[]>(
  arr: A & ([LLMService] extends [A[number]] ? unknown : never)
) => arr)(["openai", "anthropic", "google-ai", "deepseek", "xai"] as const);

export const MODEL_FAMILY_SERVICE: {
  [f in ModelFamily]: LLMService;
} = {
  // OpenAI
  turbo: "openai",
  gpt4: "openai",
  "gpt4-turbo": "openai",
  "gpt4.5": "openai",
  "gpt4.1": "openai",
  "gpt4.1-mini": "openai",
  "gpt4.1-nano": "openai",
  gpt4o: "openai",
  "gpt4o-mini": "openai",
  "chatgpt-4o": "openai",
  "codex-mini": "openai",
  o1: "openai",
  "o1-pro": "openai",
  "o1-mini": "openai",
  o3: "openai",
  "o3-pro": "openai",
  "o3-mini": "openai",
  "o4-mini": "openai",

  // Anthropic
  claude: "anthropic",
  "claude-opus": "anthropic",

  // Google
  "gemini-flash-lite": "google-ai",
  "gemini-flash": "google-ai",
  "gemini-pro": "google-ai",

  // Deepseek
  "deepseek-chat": "deepseek",
  "deepseek-reasoner": "deepseek",

  // XAI
  grok: "xai",
};

export const OPENAI_MODEL_FAMILY_MAP: { [regex: string]: OpenAIModelFamily } = {
  "^gpt-3.5-turbo": "turbo",

  "^gpt-4$": "gpt4",
  "^gpt-4-\\d{4}$": "gpt4",

  "^gpt-4-turbo(-preview)?$": "gpt4-turbo",
  "^gpt-4-(0125|1106)(-preview)?$": "gpt4-turbo",
  "^gpt-4(-\\d{4})?-vision(-preview)?$": "gpt4-turbo",
  "^gpt-4-turbo(-\\d{4}-\\d{2}-\\d{2})?$": "gpt4-turbo",

  "^chatgpt-4o": "chatgpt-4o",
  "^gpt-4o(-\\d{4}-\\d{2}-\\d{2})?$": "gpt4o",
  "^gpt-4o-mini(-\\d{4}-\\d{2}-\\d{2})?$": "gpt4o-mini",

  "^codex-mini": "codex-mini",

  "^gpt-4.5(-preview)?(-\\d{4}-\\d{2}-\\d{2})?$": "gpt4.5", // Legacy: Deprecate on 2025-07-14

  "^gpt-4.1(-\\d{4}-\\d{2}-\\d{2})?$": "gpt4.1",
  "^gpt-4.1-mini(-\\d{4}-\\d{2}-\\d{2})?$": "gpt4.1-mini",
  "^gpt-4.1-nano(-\\d{4}-\\d{2}-\\d{2})?$": "gpt4.1-nano",

  "^o1-mini(-\\d{4}-\\d{2}-\\d{2})?$": "o1-mini", // Legacy: Deprecate on 2025-10-27
  "^o1-preview-\\d{4}-\\d{2}-\\d{2}$": "o1", // Legacy: Deprecate on 2025-07-28
  "^o1(-\\d{4}-\\d{2}-\\d{2})?$": "o1",
  "^o1-pro(-\\d{4}-\\d{2}-\\d{2})?$": "o1-pro",

  "^o3(-\\d{4}-\\d{2}-\\d{2})?$": "o3",
  "^o3-pro(-\\d{4}-\\d{2}-\\d{2})?$": "o3-pro",
  "^o3-mini(-\\d{4}-\\d{2}-\\d{2})?$": "o3-mini",
  "^o4-mini(-\\d{4}-\\d{2}-\\d{2})?$": "o4-mini",
};

export function getOpenAIModelFamily(
  model: string,
  defaultFamily?: OpenAIModelFamily
): OpenAIModelFamily;
export function getOpenAIModelFamily(
  model: string,
  defaultFamily: OpenAIModelFamily | null
): OpenAIModelFamily | null;

export function getOpenAIModelFamily(
  model: string,
  defaultFamily: OpenAIModelFamily | null = "gpt4"
): OpenAIModelFamily | null {
  for (const [regex, family] of Object.entries(OPENAI_MODEL_FAMILY_MAP)) {
    if (model.match(regex)) return family;
  }
  return defaultFamily;
}

export function getClaudeModelFamily(model: string): AnthropicModelFamily {
  if (model.includes("opus")) return "claude-opus";
  return "claude";
}

export function getGoogleAIModelFamily(model: string): GoogleAIModelFamily {
  if (model.includes("flash-lite")) return "gemini-flash-lite";
  return model.includes("flash") ? "gemini-flash" : "gemini-pro";
}

export function getDeepSeekModelFamily(model: string): DeepSeekModelFamily {
  return model.includes("reasoner") ? "deepseek-reasoner" : "deepseek-chat";
}

export function getXAIModelFamily(model: string): XAIModelFamily {
  return "grok";
}

export function assertIsKnownModelFamily(modelFamily: string): asserts modelFamily is ModelFamily {
  if (!MODEL_FAMILIES.includes(modelFamily as ModelFamily)) {
    throw new Error(`Unknown model family: ${modelFamily}`);
  }
}

export function getModelFamilyForRequest(req: Request): ModelFamily {
  if (req.modelFamily) return req.modelFamily;
  // There is a single request queue, but it is partitioned by model family.
  // Model families are typically separated on cost/rate limit boundaries so
  // they should be treated as separate queues.
  const model = req.body.model ?? "gpt-3.5-turbo";
  let modelFamily: ModelFamily;

  // Weird special case for Deepseek because they serve models with
  // different API formats, so the outbound API alone is not sufficient to
  // determine the partition.
  if (req.service === "deepseek") {
    modelFamily = getDeepSeekModelFamily(model);
  } else if (req.service === "xai") {
    modelFamily = getXAIModelFamily(model);
  } else {
    switch (req.outboundApi) {
      case "anthropic-chat":
        modelFamily = getClaudeModelFamily(model);
        break;
      case "openai":
        modelFamily = getOpenAIModelFamily(model);
        break;
      case "google-ai":
        modelFamily = getGoogleAIModelFamily(model);
        break;
      default:
        assertNever(req.outboundApi);
    }
  }

  return (req.modelFamily = modelFamily);
}

export function getModelFamilyForModel(req: Request): ModelFamily {
  const model = req.body.model?.trim();
  if (!model) return "" as ModelFamily;

  if (model.includes("deepseek")) return getDeepSeekModelFamily(model);
  if (model.includes("claude")) return getClaudeModelFamily(model);
  if (model.includes("gemini")) return getGoogleAIModelFamily(model);
  if (model.includes("grok")) return getXAIModelFamily(model);

  const modelFamily = getOpenAIModelFamily(model, null);
  if (modelFamily) return modelFamily;

  req.log.warn({ model }, "Unknown model, returning empty string.");

  // Return empty string if the model is unknown.
  return "" as ModelFamily;
}

function assertNever(x: never): never {
  throw new Error(`Called assertNever with argument ${x}.`);
}

function checkModel(model: string) {
  for (const pattern of config.disallowedModels) {
    if (typeof pattern === "string") {
      if (model.includes(pattern)) return false;
    }
    if (pattern instanceof RegExp) {
      if (pattern.test(model)) return false;
    }
  }
  return true;
}

/**
 * Check if the provided model string does not match any of the forbidden patterns.
 *
 * @param input - A string or an array of strings representing model ids.
 * @returns true if none of the provided models are forbidden, false otherwise.
 */
export function isAllowedModel<T extends string>(input: T | T[]) {
  return Array.isArray(input) ? input.every(checkModel) : checkModel(input);
}

/**
 * Filter out forbidden model strings from an array.
 *
 * @param models - An array of model strings to filter.
 * @returns A filtered array containing only allowed model strings.
 */
export function filterAllowedModels<T extends string>(models: T[]): T[] {
  return models.filter(checkModel);
}

export function getModelMaxContextLimit(model: string): number | null {
  if (model.includes("gpt")) {
    // GPT-3.5: https://platform.openai.com/docs/models/gpt-3.5-turbo
    if (model.match(/^gpt-3.5-turbo/)) return 16_384;

    // GPT-4: https://platform.openai.com/docs/models/gpt-4
    if (model.match(/^gpt-4(-\d{4})?$/)) return 8_192;

    // GPT-4o: https://platform.openai.com/docs/models/gpt-4o
    if (model.match(/^gpt-4o/)) return 128_000;
    if (model.match(/^chatgpt-4o/)) return 128_000;

    // GPT-4 Turbo: https://platform.openai.com/docs/models/gpt-4-turbo
    if (model.match(/^gpt-4-turbo(-preview)?/)) return 128_000;
    if (model.match(/^gpt-4-(0125|1106)(-preview)?$/)) return 128_000;
    if (model.match(/^gpt-4(-\d{4})?-vision(-preview)?$/)) return 128_000;

    // GPT-4.1: https://platform.openai.com/docs/models/gpt-4.1
    if (model.match(/^gpt-4.1/)) return 1_047_576;
    // GPT-4.1 Mini: https://platform.openai.com/docs/models/gpt-4.1-mini
    if (model.match(/^gpt-4.1-mini/)) return 1_047_576;
    // GPT-4.1 Nano: https://platform.openai.com/docs/models/gpt-4.1-nano
    if (model.match(/^gpt-4.1-nano/)) return 1_047_576;

    // GPT-4.5: https://platform.openai.com/docs/models/gpt-4.5-preview
    if (model.match(/^gpt-4.5(-preview)?/)) return 128_000;
  }

  if (model.includes("codex")) {
    // Codex-mini-latest: https://platform.openai.com/docs/models/codex-mini-latest
    if (model.match(/^codex-mini/)) return 200_000;
  }

  if (model.includes("o1")) {
    // OpenAI o1: https://platform.openai.com/docs/models/o1
    if (model.match(/^o1(-\d{4}-\d{2}-\d{2})?$/)) return 200_000;
    if (model.match(/^o1-pro(-\d{4}-\d{2}-\d{2})?$/)) return 200_000;
    if (model.match(/^o1-preview(-\d{4}-\d{2}-\d{2})?$/)) return 128_000;

    // OpenAI o1 mini: https://platform.openai.com/docs/models/o1-mini
    if (model.match(/^o1-mini(-\d{4}-\d{2}-\d{2})?$/)) return 128_000;
  }

  if (model.includes("o3")) {
    // OpenAI o3: https://platform.openai.com/docs/models/o3
    if (model.match(/^o3(-\d{4}-\d{2}-\d{2})?$/)) return 200_000;

    // OpenAI o3 pro: https://platform.openai.com/docs/models/o3-pro
    if (model.match(/^o3-pro(-\d{4}-\d{2}-\d{2})?$/)) return 200_000;

    // OpenAI o3 mini: https://platform.openai.com/docs/models/o3-mini
    if (model.match(/^o3-mini(-\d{4}-\d{2}-\d{2})?$/)) return 200_000;
  }

  if (model.includes("o4")) {
    // OpenAI o4 mini: https://platform.openai.com/docs/models/o4-mini
    if (model.match(/^o4-mini(-\d{4}-\d{2}-\d{2})?$/)) return 200_000;
  }

  // Claude: https://docs.anthropic.com/en/docs/about-claude/models/all-models
  if (model.match(/^claude-3/)) return 200_000;
  if (model.match(/^claude-(opus|sonnet)-4/)) return 200_000;

  // Deepseek: https://api-docs.deepseek.com/quick_start/pricing
  if (model.match(/^deepseek-/)) return 65_536;

  // Gemini: https://ai.google.dev/gemini-api/docs/models#gemini-api
  if (model.match(/^gemini-/)) return 1_048_576;

  // Unknown model
  return null;
}

export function getModelMaxOutputLimit(req: Request) {
  const model = req.body.model as string;

  if (model.includes("gpt")) {
    // GPT-3.5: https://platform.openai.com/docs/models/gpt-3.5-turbo
    if (model.match(/^gpt-3.5-turbo/)) return 4_096;

    // GPT-4: https://platform.openai.com/docs/models/gpt-4
    if (model.match(/^gpt-4(-\d{4})?$/)) return 8_192;

    // GPT-4o: https://platform.openai.com/docs/models/gpt-4o
    if (model.match(/^gpt-4o/)) return 16_384;
    if (model.match(/^chatgpt-4o/)) return 16_384;

    // GPT-4 Turbo: https://platform.openai.com/docs/models/gpt-4-turbo
    if (model.match(/^gpt-4-turbo(-preview)?/)) return 4_096;
    if (model.match(/^gpt-4-(0125|1106)(-preview)?$/)) return 4_096;
    if (model.match(/^gpt-4(-\d{4})?-vision(-preview)?$/)) return 4_096;

    // GPT-4.1: https://platform.openai.com/docs/models/gpt-4.1
    if (model.match(/^gpt-4.1/)) return 32_768;
    // GPT-4.1 Mini: https://platform.openai.com/docs/models/gpt-4.1-mini
    if (model.match(/^gpt-4.1-mini/)) return 32_768;
    // GPT-4.1 Nano: https://platform.openai.com/docs/models/gpt-4.1-nano
    if (model.match(/^gpt-4.1-nano/)) return 32_768;

    // GPT-4.5: https://platform.openai.com/docs/models/gpt-4.5-preview
    if (model.match(/^gpt-4.5(-preview)?/)) return 16_384;
  }

  if (model.includes("codex")) {
    // Codex-mini-latest: https://platform.openai.com/docs/models/codex-mini-latest
    if (model.match(/^codex-mini/)) return 100_000;
  }

  if (model.includes("o1")) {
    // OpenAI o1: https://platform.openai.com/docs/models/o1
    if (model.match(/^o1(-\d{4}-\d{2}-\d{2})?$/)) return 100_000;
    if (model.match(/^o1-pro(-\d{4}-\d{2}-\d{2})?$/)) return 100_000;
    if (model.match(/^o1-preview(-\d{4}-\d{2}-\d{2})?$/)) return 32_768;

    // OpenAI o1 mini: https://platform.openai.com/docs/models/o1-mini
    if (model.match(/^o1-mini(-\d{4}-\d{2}-\d{2})?$/)) return 65_536;
  }

  if (model.includes("o3")) {
    // OpenAI o3: https://platform.openai.com/docs/models/o3
    if (model.match(/^o3(-\d{4}-\d{2}-\d{2})?$/)) return 100_000;

    // OpenAI o3 pro: https://platform.openai.com/docs/models/o3-pro
    if (model.match(/^o3-pro(-\d{4}-\d{2}-\d{2})?$/)) return 100_000;

    // OpenAI o3 mini: https://platform.openai.com/docs/models/o3-mini
    if (model.match(/^o3-mini(-\d{4}-\d{2}-\d{2})?$/)) return 100_000;
  }

  if (model.includes("o4")) {
    // OpenAI o4 mini: https://platform.openai.com/docs/models/o4-mini
    if (model.match(/^o4-mini(-\d{4}-\d{2}-\d{2})?$/)) return 100_000;
  }

  if (model.includes("claude")) {
    // Claude: https://docs.anthropic.com/en/docs/about-claude/models/all-models
    if (model.match(/^claude-3-7/)) return 64_000;
    if (model.match(/^claude-3-5/)) return 8_192;
    if (model.match(/^claude-3-(sonnet|opus|haiku)/)) return 4_096;

    if (model.match(/^claude-opus-4/)) return 32_000;
    if (model.match(/^claude-sonnet-4/)) return 32_000;
  }

  if (model.includes("gemini")) {
    // Gemini 2.5 Pro: https://ai.google.dev/gemini-api/docs/models#gemini-2.5-pro-preview-03-25
    if (model.match(/^gemini-2.5-pro/)) return 65_536;

    // Gemini 2.5 Flash: https://ai.google.dev/gemini-api/docs/models#gemini-2.5-flash-preview-03-25
    if (model.match(/^gemini-2.5-flash/)) return 65_536;

    // Gemini 2.0 Flash: https://ai.google.dev/gemini-api/docs/models#gemini-2.0-flash
    if (model.match(/^gemini-2.0-flash/)) return 8_192;

    // Gemini 2.0 Flash Lite: https://ai.google.dev/gemini-api/docs/models#gemini-2.0-flash-lite
    if (model.match(/^gemini-2.0-flash-lite/)) return 8_192;

    // Remaining Gemini (1.5 flash, 1.5 pro, etc...)
    if (model.match(/^gemini-/)) return 8_192;
  }

  // Deepseek: https://api-docs.deepseek.com/quick_start/pricing
  if (model.match(/^deepseek-/)) return 8_192;

  req.log.warn(
    { model },
    "Unknown model, using 4k output limit. Consider updating the check to ensure it is correct."
  );

  // Default to 4k max output if model is unknown
  return 4_096;
}
