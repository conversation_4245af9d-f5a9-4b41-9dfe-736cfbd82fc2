import { and, eq, getTableColumns, sql } from "drizzle-orm";
import { Router } from "express";
import { z } from "zod/v4";

import { config } from "@/config";
import { buildInfo } from "@/service-info";

import { getDatabase } from "@/shared/database";
import * as schema from "@/shared/database/schema";
import type { ModelFamily } from "@/shared/models";
import { getTokenCostUsd } from "@/shared/stats";
import { getUserUsages, hashIp } from "@/shared/users/user-store";
import { ObjectTyped } from "@/shared/utils";

const router = Router();

/**
 * Get all chat completion events.
 * GET /admin/api/events/chat/logs
 *
 * @param limit - The number of records to return (default: 500)
 *
 * @returns The list of events
 */
router.get("/chat/logs", async (req, res) => {
  const limit = z.coerce.number().default(500).parse(req.query.limit);
  const db = getDatabase();

  if (!config.eventLogging) {
    return res.status(404).json({ error: { message: "Event logging is disabled" } });
  }

  const data = db.query.users_events.findMany({
    where: (fields, { eq }) => eq(fields.type, "chat-completion"),
    orderBy: (fields, { desc }) => desc(fields.createdAt),
    limit,
    columns: { id: false, type: false },
    extras: (field, { sql }) => ({
      inputTokens: sql<number>`json_extract(${field.payload}, '$.inputTokens')`.as("inputTokens"),
      outputTokens: sql<number>`json_extract(${field.payload}, '$.outputTokens')`.as(
        "outputTokens"
      ),
      modelFamily: sql<ModelFamily>`json_extract(${field.payload}, '$.family')`.as("modelFamily"),
    }),
  });

  const ips = await db
    .selectDistinct({ ips: sql<string | null>`json_extract(payload, '$.ip')` })
    .from(schema.users_events);

  const userCount = await db
    .select({
      userCount: sql<number>`COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END)`,
    })
    .from(schema.users_events);

  const ipsCount = new Set(
    ips
      .map(({ ips }) => ips)
      .filter((ips) => ips !== null)
      .map(hashIp)
  );

  const logs = (await data).map((log) => {
    const cost = getTokenCostUsd(log);

    return {
      modelFamily: log.modelFamily,
      input: { tokens: log.inputTokens, cost: cost.input },
      output: { tokens: log.outputTokens, cost: cost.output },
      createdAt: log.createdAt,
      payload: log.payload,
      userToken: log.userToken,
    };
  });

  return res.json({ logs, ipsCount: ipsCount.size, userCount: userCount[0].userCount });
});

/**
 * Returns events for the given user token.
 * GET /admin/api/events/:token/logs
 *
 * @param token - The user token
 * @param type - The type of event to filter by (optional)
 * @param skip - The number of records to skip (default: 0)
 * @param limit - The number of records to return (default: 200)
 * @param sorting - The sorting configuration (default: createdAt asc)
 *
 * @returns The list of events
 */
router.get("/:token/logs", async (req, res) => {
  if (!config.eventLogging) {
    return res.status(404).json({ error: { message: "Event logging is disabled" } });
  }

  const allowSortingKeys = ObjectTyped.keys(getTableColumns(schema.users_events));

  const logSchema = z.object({
    token: z.string(),
    type: z.enum(["new-ip", "user-action", "chat-completion"]).optional(),
    skip: z.coerce.number().default(0),
    limit: z.coerce.number().default(200),
    sorting: z
      .object({
        table: z.enum(allowSortingKeys),
        direction: z.enum(["asc", "desc"]),
      })
      .default({ direction: "asc", table: "createdAt" }),
  });

  const args = logSchema.safeParse({ ...req.params, ...req.query });
  if (!args.success) {
    return res.status(400).json({ error: args.error });
  }

  const filter = and(
    eq(schema.users_events.userToken, args.data.token),
    args.data.type ? eq(schema.users_events.type, args.data.type) : undefined
  );

  const db = getDatabase();

  const countPromise = db.$count(schema.users_events, filter);
  const dataPromise = db.query.users_events.findMany({
    where: filter,
    limit: args.data.limit,
    offset: args.data.skip,
    orderBy: (_, { asc, desc, sql }) =>
      args.data.sorting.direction === "asc"
        ? asc(sql.raw(args.data.sorting.table))
        : desc(sql.raw(args.data.sorting.table)),
  });

  const [data, count] = await Promise.all([dataPromise, countPromise]);
  return res.json({ count, data });
});

/**
 * Get global statistics for the admin dashboard.
 * GET /admin/api/events/stats
 *
 * @returns Global statistics including most used model, unique IPs, total tokens, costs, etc.
 */
router.get("/stats", async (req, res) => {
  const db = getDatabase();

  if (!config.eventLogging) {
    return res.status(404).json({ error: { message: "Event logging is disabled" } });
  }

  try {
    // Get chat completion logs to calculate most used model
    const logsData = db.query.users_events.findMany({
      where: (fields, { eq }) => eq(fields.type, "chat-completion"),
      orderBy: (fields, { desc }) => desc(fields.createdAt),
      limit: 1000, // Limit to recent logs for performance
      columns: { id: false, type: false },
      extras: (field, { sql }) => ({
        inputTokens: sql<number>`json_extract(${field.payload}, '$.inputTokens')`.as("inputTokens"),
        outputTokens: sql<number>`json_extract(${field.payload}, '$.outputTokens')`.as(
          "outputTokens"
        ),
        modelFamily: sql<ModelFamily>`json_extract(${field.payload}, '$.family')`.as("modelFamily"),
        model: sql<string>`json_extract(${field.payload}, '$.model')`.as("model"),
      }),
    });

    // Get unique IPs count
    const ips = await db
      .selectDistinct({ ips: sql<string | null>`json_extract(payload, '$.ip')` })
      .from(schema.users_events);

    const ipsCount = new Set(
      ips
        .map(({ ips }) => ips)
        .filter((ips) => ips !== null)
        .map(hashIp)
    );

    // Get user count
    const userCount = await db
      .select({
        userCount: sql<number>`COUNT(DISTINCT CASE WHEN userToken != 'No token' THEN userToken END)`,
      })
      .from(schema.users_events);

    // Get global usage statistics
    const usageStats = await getUserUsages();

    // Calculate most used model
    const logs = await logsData;
    const modelCounts: Record<string, number> = {};
    logs.forEach((log) => {
      const model = log.model;

      if (!modelCounts[model]) modelCounts[model] = 0;
      modelCounts[model]++;
    });

    // Find the model with the highest count
    let mostUsedModel = null;
    let highestCount = 0;

    Object.entries(modelCounts).forEach(([model, count]) => {
      if (count > highestCount) {
        mostUsedModel = model;
        highestCount = count;
      }
    });

    // Get service info for uptime
    const baseUrl = req.protocol + "://" + req.get("host") + config.proxyEndpointRoute;
    const serviceInfo = await buildInfo(baseUrl, true);

    // Combine all statistics
    const stats = {
      mostUsedModel: {
        model: mostUsedModel,
        count: highestCount,
      },
      ipsCount: ipsCount.size,
      userCount: userCount[0].userCount,
      uptime: serviceInfo.uptime,
      totalPrompts: usageStats.totalPrompts,
      inputTokens: usageStats.inputTokens,
      outputTokens: usageStats.outputTokens,
      totalTokens: usageStats.inputTokens + usageStats.outputTokens,
      inputCost: usageStats.inputCost,
      outputCost: usageStats.outputCost,
      totalCost: usageStats.inputCost + usageStats.outputCost,
    };

    return res.json(stats);
  } catch (error) {
    req.log.error({ error }, "Failed to fetch global statistics");
    return res.status(500).json({ error: { message: "Failed to fetch global statistics" } });
  }
});

export { router as eventsApiRouter };
