import express from "express";

import type { APIFormat } from "@/shared/key-management";
import { assertNever } from "@/shared/utils";

import {
  mergeEventsForAnthropicChat,
  mergeEventsForGoogleAI,
  mergeEventsForOpenAIChat,
  type AnthropicV2StreamEvent,
  type GoogleAIStreamEvent,
  type OpenAIChatCompletionStreamEvent,
} from "./index";

/**
 * Collects SSE events containing incremental chat completion responses and
 * compiles them into a single finalized response for downstream middleware.
 */
export class EventAggregator {
  private readonly model: string;
  private readonly requestFormat: APIFormat;
  private readonly responseFormat: APIFormat;
  private readonly events: OpenAIChatCompletionStreamEvent[];

  constructor({ body, inboundApi, outboundApi }: express.Request) {
    this.events = [];
    this.requestFormat = inboundApi;
    this.responseFormat = outboundApi;
    this.model = body.model;
  }

  addEvent(event: OpenAIChatCompletionStreamEvent | AnthropicV2StreamEvent | GoogleAIStreamEvent) {
    if (eventIsOpenAIEvent(event)) {
      this.events.push(event);
    } else {
      // horrible special case. previously all transformers' target format was
      // openai, so the event aggregator could conveniently assume all incoming
      // events were in openai format.
      // now we have added some transformers that convert between non-openai
      // formats, so aggregator needs to know how to collapse for more than
      // just openai.
      // because writing aggregation logic for every possible output format is
      // annoying, we will just transform any non-openai output events to openai
      // format (even if the client did not request openai at all) so that we
      // still only need to write aggregators for openai SSEs.
      let openAIEvent: OpenAIChatCompletionStreamEvent | undefined;

      if (openAIEvent) {
        this.events.push(openAIEvent);
      }
    }
  }

  getFinalResponse() {
    switch (this.responseFormat) {
      case "openai":
        return mergeEventsForOpenAIChat(this.events);

      case "google-ai":
        return mergeEventsForGoogleAI(this.events);

      case "anthropic-chat":
        return mergeEventsForAnthropicChat(this.events);

      default:
        assertNever(this.responseFormat);
    }
  }

  hasEvents() {
    return this.events.length > 0;
  }
}

function eventIsOpenAIEvent(event: any): event is OpenAIChatCompletionStreamEvent {
  return event?.object === "chat.completion.chunk";
}
