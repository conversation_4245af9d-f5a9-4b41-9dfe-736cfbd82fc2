<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "List Keys" }) %>
  </head>
  <body class="bg-background font-mono text-white">
    <div x-data="keyManager" class="mx-auto flex min-h-svh w-full flex-col gap-4 p-4">
      <h1 class="mt-2 mb-6 text-center text-3xl font-bold">List Keys</h1>

      <!-- Message Section -->
      <template x-if="error || message">
        <div class="mb-8 w-full">
          <!-- Error Message -->
          <template x-if="error">
            <div class="card bg-background-error border-border-error mb-4 p-4 text-center">
              <p class="text-lg" x-text="error"></p>
              <button @click="error = null" class="danger-button mt-2 px-4 py-2">Dismiss</button>
            </div>
          </template>

          <!-- Success message -->
          <template x-if="message">
            <div class="card border-border-success bg-background-success mb-4 p-4 text-center">
              <p class="text-lg" x-text="message"></p>
              <button @click="message = null" class="success-button mt-2 px-4 py-2">Dismiss</button>
            </div>
          </template>
        </div>
      </template>

      <!-- Service Navigation -->
      <div class="card mx-auto mb-4 w-full max-w-7xl overflow-hidden p-0">
        <nav class="flex w-full">
          <% services.forEach(apiService => { %>
          <a
            href="/admin/manage/list-keys?service=<%= apiService %>"
            class="border-border hover:bg-background-secondary flex-1 border-r p-4 text-center font-bold tracking-wider uppercase transition-colors last:border-r-0"
            :class="{ 'bg-accent text-white': '<%= service === apiService %>' === 'true' }"
            data-active="<%= service === apiService %>"
          >
            <%= apiService %>
          </a>
          <% }) %>
        </nav>
      </div>

      <!-- Statistics Section -->
      <div class="card mx-auto mb-4 w-full max-w-7xl p-4" data-service="<%= service %>">
        <h2 class="mb-4 text-center text-2xl font-bold tracking-wider uppercase">Statistics</h2>

        <div class="flex flex-wrap items-center justify-center gap-4" x-show="!loading">
          <div class="bg-background-secondary flex items-center gap-2 rounded-lg p-3 shadow">
            <span class="text-base">Total: </span>
            <span
              :class="totalCount > 0 ? 'text-success' : 'text-danger'"
              class="text-base font-bold"
              x-text="totalCount"
            ></span>
          </div>

          <div class="bg-background-secondary flex items-center gap-2 rounded-lg p-3 shadow">
            <span class="text-base">Working: </span>
            <span
              :class="keys.filter(key => !key.isDisabled).length > 0 ? 'text-success' : 'text-danger'"
              class="text-base font-bold"
              x-text="keys.filter(key => !key.isDisabled).length"
            ></span>
          </div>

          <div class="bg-background-secondary flex items-center gap-2 rounded-lg p-3 shadow">
            <span class="text-base">OverQuota: </span>
            <span
              :class="keys.filter(key => key.isOverQuota).length > 0 ? 'text-success' : 'text-danger'"
              class="text-base font-bold"
              x-text="keys.filter(key => key.isOverQuota).length"
            ></span>
          </div>

          <div class="bg-background-secondary flex items-center gap-2 rounded-lg p-3 shadow">
            <span class="text-base">Revoked: </span>
            <span
              :class="keys.filter(key => key.isRevoked).length > 0 ? 'text-success' : 'text-danger'"
              class="text-base font-bold"
              x-text="keys.filter(key => key.isRevoked).length"
            ></span>
          </div>
        </div>

        <!-- Loading indicator for statistics -->
        <div class="flex items-center justify-center p-4" x-show="loading">
          <div class="flex items-center gap-2">
            <span class="animate-spin text-xl">🔄</span>
            <span>Loading statistics...</span>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-4 grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4">
          <div>
            <form id="addKey" action="/admin/manage/list-keys/add-keys" method="POST">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
              <input type="hidden" name="service" value="<%= service %>" />
              <button type="button" @click="promptAddKeys($event)" class="primary-button w-full">
                <span class="mr-2 inline-block text-base">➕</span> Add Key
              </button>
            </form>
          </div>

          <div>
            <form id="recheck" action="/admin/manage/maintenance" method="POST">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
              <input type="hidden" name="action" value="recheck" />
              <button
                type="button"
                @click="promptRecheckKeys($event)"
                class="secondary-button w-full"
              >
                <span class="mr-2 inline-block text-base">🔄</span> Recheck Keys
              </button>
            </form>
          </div>

          <div>
            <form action="/admin/manage/list-keys/delete-keys" method="post">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
              <input type="hidden" name="service" value="<%= service %>" />
              <button
                type="button"
                @click="promptRemoveKeys($event, 'overQuota')"
                class="danger-button w-full"
              >
                <span class="mr-2 inline-block text-base">🗑️</span> Remove Over Quota
              </button>
            </form>
          </div>

          <div>
            <form action="/admin/manage/list-keys/delete-keys" method="post">
              <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
              <input type="hidden" name="service" value="<%= service %>" />
              <button
                type="button"
                @click="promptRemoveKeys($event, 'revoked')"
                class="danger-button w-full"
              >
                <span class="mr-2 inline-block text-base">🗑️</span> Remove Revoked
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Table Section -->
      <div class="card mb-4 overflow-x-auto p-0" x-show="!loading">
        <table class="h-px w-full border-separate border-spacing-0 overflow-hidden p-0">
          <thead class="bg-background-secondary">
            <tr class="text-left">
              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('hash')"
                  :class="{ 'text-accent-light': sortField === 'hash' }"
                >
                  Key
                </a>
              </th>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('modelFamilies')"
                  :class="{ 'text-accent-light': sortField === 'modelFamilies' }"
                >
                  Model Families
                </a>
              </th>

              <template x-if="service === 'openai'">
                <th class="text-accent border-border border-b p-2 font-bold">
                  <a
                    href="#"
                    @click.prevent="sortBy('organizationId')"
                    :class="{ 'text-accent-light': sortField === 'organizationId' }"
                  >
                    Org Id
                  </a>
                </th>
              </template>

              <template x-if="service !== 'deepseek'">
                <th class="text-accent border-border border-b p-2 font-bold">
                  <a
                    href="#"
                    @click.prevent="sortBy('tier')"
                    :class="{ 'text-accent-light': sortField === 'tier' }"
                  >
                    Tier
                  </a>
                </th>
              </template>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('prompts')"
                  :class="{ 'text-accent-light': sortField === 'prompts' }"
                >
                  Prompts
                </a>
              </th>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('input.tokens')"
                  :class="{ 'text-accent-light': sortField === 'input.tokens' }"
                >
                  Input
                </a>
              </th>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('output.tokens')"
                  :class="{ 'text-accent-light': sortField === 'output.tokens' }"
                >
                  Output
                </a>
              </th>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('addedAt')"
                  :class="{ 'text-accent-light': sortField === 'addedAt' }"
                >
                  Added
                </a>
              </th>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('lastCheckedAt')"
                  :class="{ 'text-accent-light': sortField === 'lastCheckedAt' }"
                >
                  Last Check
                </a>
              </th>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('lastUsedAt')"
                  :class="{ 'text-accent-light': sortField === 'lastUsedAt' }"
                >
                  Last Used
                </a>
              </th>

              <th class="text-accent border-border border-b p-2 font-bold">
                <a
                  href="#"
                  @click.prevent="sortBy('status')"
                  :class="{ 'text-accent-light': sortField === 'status' }"
                >
                  Status
                </a>
              </th>

              <th
                class="text-accent border-border border-b p-2 text-center font-bold tracking-wider uppercase"
              >
                Actions
              </th>
            </tr>
          </thead>

          <tbody>
            <template x-for="(key, index) in paginatedKeys" :key="key.hash">
              <tr
                class="*:border-border text-sm transition-colors *:border-b hover:bg-[rgba(255,255,255,0.1)]"
                :class="{ 'bg-background-tertiary': index % 2 === 1, '*:border-none': index === paginatedKeys.length - 1 }"
              >
                <td class="p-2 px-3">
                  <a
                    :href="`/admin/manage/view-key/<%= service %>/${key.hash}`"
                    class="text-accent hover:text-accent-light"
                  >
                    <code class="text-sm" x-text="key.hash"></code>
                  </a>
                </td>
                <td class="max-w-xs p-2 px-3">
                  <div class="flex flex-wrap gap-1">
                    <template
                      x-for="model in key.modelFamilies.sort((a, b) => a.localeCompare(b))"
                      :key="model"
                    >
                      <code class="w-max text-xs" x-text="model"></code>
                    </template>
                  </div>
                </td>

                <template x-if="service === 'openai'">
                  <td class="p-2 px-3">
                    <code x-text="key.organizationId ? key.organizationId.slice(4, 12) : 'No Org'">
                    </code>
                  </td>
                </template>

                <template x-if="'tier' in key">
                  <td class="p-2 px-3" x-text="key.tier"></td>
                </template>

                <td class="p-2 px-3" x-text="formatNumber(key.prompts)"></td>
                <td class="p-2 px-3" x-text="formatNumber(key.input.tokens)"></td>
                <td class="p-2 px-3" x-text="formatNumber(key.output.tokens)"></td>

                <td class="p-2 px-3" x-text="formatDate(key.addedAt)"></td>
                <td class="p-2 px-3" x-text="formatDate(key.lastCheckedAt)"></td>
                <td class="p-2 px-3" x-text="formatDate(key.lastUsedAt)"></td>

                <td class="p-2 px-3">
                  <span
                    class="font-bold"
                    :class="{ 'text-success': !key.isDisabled, 'text-danger': key.isDisabled || key.isRevoked, 'text-warning': key.isPozzed || key.isOverQuota }"
                    x-text="key.status"
                  ></span>
                </td>

                <td class="h-full">
                  <div class="flex h-full items-center *:flex-1">
                    <template x-if="!key.isDisabled">
                      <button
                        class="border-border hover:bg-warning flex h-full w-full cursor-pointer items-center justify-center border-l transition-colors"
                        type="button"
                        @click="promptDisableKey($event, key.hash, false)"
                        :data-hash="key.hash"
                        title="Disable key"
                      >
                        <span class="inline-block text-base">⛔</span>
                      </button>
                    </template>

                    <template x-if="key.isDisabled">
                      <div class="contents">
                        <button
                          class="border-border hover:bg-success flex h-full w-full cursor-pointer items-center justify-center border-l transition-colors"
                          type="button"
                          @click="promptDisableKey($event, key.hash, true)"
                          :data-hash="key.hash"
                          title="Reactivate key"
                        >
                          <span class="inline-block text-base">✅</span>
                        </button>

                        <button
                          class="border-border hover:bg-danger flex h-full w-full cursor-pointer items-center justify-center border-l transition-colors"
                          type="button"
                          @click="promptRemoveKeys($event, 'key')"
                          :data-hash="key.hash"
                          title="Delete key"
                        >
                          <span class="inline-block text-base">🗑️</span>
                        </button>
                      </div>
                    </template>
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>

      <!-- Loading indicator for table -->
      <div class="card mb-4 p-8 text-center" x-show="loading">
        <div class="flex flex-col items-center justify-center gap-4">
          <div class="animate-spin text-4xl">🔄</div>
          <p class="text-lg">Loading keys...</p>
        </div>
      </div>

      <!-- Pagination -->
      <div id="pagination" class="mt-4 mb-2" x-show="!loading && totalCount > 0">
        <div class="flex flex-col items-center justify-center gap-4">
          <div class="flex items-center justify-center gap-2">
            <button
              @click="changePage('prev')"
              :disabled="currentPage === 1"
              :class="{ 'cursor-not-allowed bg-[#25313c]': currentPage === 1 }"
              class="primary-button"
            >
              &laquo; Prev
            </button>

            <template x-if="pageCount <= 7">
              <div class="flex items-center gap-2">
                <template x-for="page in pageNumbers" :key="page">
                  <button
                    class="primary-button flex h-10 w-10 items-center justify-center p-0"
                    :class="{ 'bg-accent-hover': page === currentPage }"
                    @click="changePage(page)"
                    x-text="page"
                  ></button>
                </template>
              </div>
            </template>

            <template x-if="pageCount > 7">
              <div class="flex items-center gap-2">
                <!-- First page -->
                <button
                  class="primary-button flex h-10 w-10 items-center justify-center p-0"
                  :class="{ 'bg-accent-hover': 1 === currentPage }"
                  @click="changePage(1)"
                  x-text="1"
                ></button>

                <!-- Ellipsis for pages before current -->
                <template x-if="currentPage > 3">
                  <span class="px-2">...</span>
                </template>

                <!-- Pages around current -->
                <template
                  x-for="page in pageNumbers.filter(p => p !== 1 && p !== pageCount && Math.abs(p - currentPage) <= 1)"
                  :key="page"
                >
                  <button
                    class="primary-button flex h-10 w-10 items-center justify-center p-0"
                    :class="{ 'bg-accent-hover': page === currentPage }"
                    @click="changePage(page)"
                    x-text="page"
                  ></button>
                </template>

                <!-- Ellipsis for pages after current -->
                <template x-if="currentPage < pageCount - 2">
                  <span class="px-2">...</span>
                </template>

                <!-- Last page -->
                <button
                  class="primary-button flex h-10 w-10 items-center justify-center p-0"
                  :class="{ 'bg-accent-hover': pageCount === currentPage }"
                  @click="changePage(pageCount)"
                  x-text="pageCount"
                ></button>
              </div>
            </template>

            <button
              @click="changePage('next')"
              :disabled="currentPage === pageCount"
              :class="{ 'cursor-not-allowed bg-[#25313c]': currentPage === pageCount }"
              class="primary-button"
            >
              Next &raquo;
            </button>
          </div>

          <p class="text-center text-base">
            <template x-if="totalCount > 0">
              <span>
                Showing <span x-text="(currentPage - 1) * pageSize + 1"></span> to
                <span x-text="Math.min(currentPage * pageSize, totalCount)"></span> of
                <span x-text="totalCount"></span> keys
              </span>
            </template>

            <template x-if="totalCount === 0">
              <span>No keys found</span>
            </template>
          </p>
        </div>
      </div>

      <script>
        document.addEventListener("alpine:init", () => {
          Alpine.data("keyManager", () => ({
            service: "<%= service %>",
            keys: [],
            loading: true,
            error: null,
            message: null,

            // Pagination
            currentPage: 1,
            pageSize: 15,

            // Sorting
            sortField: "lastUsedAt",
            sortDirection: "desc",

            dateFormatter: new Intl.DateTimeFormat("en-US", {
              dateStyle: "medium",
              timeStyle: "short",
            }),

            // Get saved sort field from localStorage or use default
            getSavedSortField() {
              try {
                const options = JSON.parse(localStorage.getItem("admin:list-keys:options")) || {};
                return options.sortField || "lastUsedAt";
              } catch (e) {
                return "lastUsedAt";
              }
            },

            // Get saved sort direction from localStorage or use default
            getSavedSortDirection() {
              try {
                const options = JSON.parse(localStorage.getItem("admin:list-keys:options")) || {};
                return options.sortDirection || "desc";
              } catch (e) {
                return "desc";
              }
            },

            getStatus(key) {
              if (key.isOverQuota) return "Over Quota";
              if (key.isRevoked) return "Revoked";
              if (key.isDisabled) return "Disabled";

              return "Active";
            },

            // Save sort options to localStorage
            saveSortOptions() {
              const options = {
                sortField: this.sortField,
                sortDirection: this.sortDirection,
              };
              localStorage.setItem("admin:list-keys:options", JSON.stringify(options));
            },

            init() {
              // Load saved sort options
              this.sortField = this.getSavedSortField();
              this.sortDirection = this.getSavedSortDirection();

              this.fetchKeys();

              // Watch for changes in sort options and save them
              this.$watch("sortField", () => this.saveSortOptions());
              this.$watch("sortDirection", () => this.saveSortOptions());
            },

            fetchKeys() {
              this.loading = true;
              this.error = null;

              fetch(`/admin/api/keys/<%= service %>`)
                .then((response) => {
                  if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                  return response.json();
                })
                .then((data) => {
                  this.loading = false;
                  this.keys = data.map((key) => ({
                    ...key,
                    status: this.getStatus(key),
                    addedAt: new Date(key.addedAt),
                    lastCheckedAt: new Date(key.lastCheckedAt),
                    lastUsedAt: key.lastUsedAt ? new Date(key.lastUsedAt) : null,
                  }));
                })
                .catch((error) => {
                  this.error = `Failed to fetch keys: ${error.message}`;
                  this.loading = false;
                  console.error("Error fetching keys:", error);
                });
            },

            getNestedValue(obj, path) {
              return path
                .split(".")
                .reduce((acc, key) => (acc != null ? acc[key] : undefined), obj);
            },

            // Computed properties
            get sortedKeys() {
              const parts = this.sortField.split(".");

              return [...this.keys].sort((a, b) => {
                const aValue = this.getNestedValue(a, this.sortField);
                const bValue = this.getNestedValue(b, this.sortField);

                // Handle null values
                if (aValue === null && bValue === null) return 0;
                if (aValue === null) return 1;
                if (bValue === null) return -1;

                // Compare based on type
                let result;
                if (typeof aValue === "string" && typeof bValue === "string") {
                  result = aValue.localeCompare(bValue);
                } else if (aValue instanceof Date && bValue instanceof Date) {
                  result = aValue.getTime() - bValue.getTime();
                } else if (Array.isArray(aValue) && Array.isArray(bValue)) {
                  result = aValue.length - bValue.length;
                } else {
                  result = String(aValue).localeCompare(String(bValue));
                }

                return this.sortDirection === "asc" ? result : -result;
              });
            },

            get totalCount() {
              return this.keys.length;
            },

            get pageCount() {
              return Math.ceil(this.totalCount / this.pageSize);
            },

            get paginatedKeys() {
              const start = (this.currentPage - 1) * this.pageSize;
              const end = start + this.pageSize;
              return this.sortedKeys.slice(start, end);
            },

            get pageNumbers() {
              const pages = [];
              for (let i = 1; i <= this.pageCount; i++) {
                pages.push(i);
              }
              return pages;
            },

            // Methods
            sortBy(field) {
              if (this.sortField === field) {
                this.sortDirection = this.sortDirection === "asc" ? "desc" : "asc";
              } else {
                this.sortField = field;
                this.sortDirection = "desc";
              }
            },

            changePage(direction) {
              if (direction === "prev") {
                this.currentPage = Math.max(1, this.currentPage - 1);
              } else if (direction === "next") {
                this.currentPage = Math.min(this.pageCount, this.currentPage + 1);
              } else if (typeof direction === "number") {
                this.currentPage = direction;
              }

              // Scroll to the pagination container after a short delay to allow DOM update
              this.$nextTick(() => {
                const paginationElement = document.getElementById("pagination");
                if (paginationElement) {
                  // Scroll with smooth behavior
                  paginationElement.scrollIntoView({ behavior: "smooth", block: "center" });
                }
              });
            },

            formatNumber(num) {
              return num.toLocaleString();
            },

            formatDate(timestamp) {
              if (!timestamp) return "Never";
              return this.dateFormatter.format(timestamp);
            },

            promptAddKeys(event) {
              const userInput = prompt("Enter the key/s (Multiple ones separated by ',')");

              if (userInput) {
                this.loading = true;

                fetch(`/admin/api/keys/<%= service %>/add`, {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({
                    keys: userInput
                      .trim()
                      .split(",")
                      .map((k) => k.trim()),
                  }),
                })
                  .then((response) => {
                    if (!response.ok) {
                      return response.json().then((data) => {
                        throw new Error(
                          data.error?.message || `HTTP error! Status: ${response.status}`
                        );
                      });
                    }
                    return response.json();
                  })
                  .then((data) => {
                    if (data.error) {
                      throw new Error(data.error.message || "Failed to add keys");
                    }
                    this.message = `Successfully added ${data.amount} keys`;
                    this.fetchKeys();
                  })
                  .catch((err) => {
                    this.error = "Failed to add keys: " + err.message;
                    this.loading = false;
                  });
              }
            },

            promptRemoveKeys(event, mode) {
              let type = mode;
              let hashes = [];

              if (mode === "key") {
                const hash = event.target.closest("button").dataset.hash;
                if (!confirm(`Are you sure you want to remove the key ${hash}?`)) return;

                type = "hash";
                hashes = [hash];
              }

              if (mode === "overQuota") {
                if (!confirm("Are you sure you want to remove all over quota keys?")) return;
              }

              if (mode === "revoked") {
                if (!confirm("Are you sure you want to remove all revoked keys?")) return;
              }

              this.loading = true;

              fetch(`/admin/api/keys/<%= service %>/delete`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(type === "hash" ? { type, hashes } : { type }),
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw Error(data.error?.message || `HTTP error! Status: ${response.status}`);
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) throw Error(data.error.message || "Failed to remove keys");

                  this.message = `Successfully removed ${data.amount} keys`;
                  this.fetchKeys();
                })
                .catch((err) => {
                  this.error = "Failed to remove keys: " + err.message;
                  this.loading = false;
                });
            },

            promptDisableKey(event, hash, reactivate = false) {
              const action = reactivate ? "enable" : "disable";
              const confirmMessage = reactivate
                ? `Are you sure you want to reactivate the key ${hash}?`
                : `Are you sure you want to disable the key ${hash}?`;

              if (confirm(confirmMessage)) {
                this.loading = true;

                fetch(`/admin/api/keys/<%= service %>/${hash}/${action}`, {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                })
                  .then((response) => {
                    if (!response.ok) {
                      return response.json().then((data) => {
                        throw new Error(
                          data.error?.message || `HTTP error! Status: ${response.status}`
                        );
                      });
                    }
                    return response.json();
                  })
                  .then((data) => {
                    if (data.error) {
                      throw new Error(data.error.message || `Failed to ${action} key`);
                    }
                    this.message = `Successfully ${reactivate ? "enabled" : "disabled"} key ${hash}`;
                    this.fetchKeys();
                  })
                  .catch((err) => {
                    this.error = `Failed to ${action} key: ${err.message}`;
                    this.loading = false;
                  });
              }
            },

            promptRecheckKeys(event) {
              if (confirm("Are you sure you want to recheck all keys?")) {
                this.loading = true;

                fetch(`/admin/api/keys/<%= service %>/recheck`, {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                })
                  .then((response) => {
                    if (!response.ok) {
                      return response.json().then((data) => {
                        throw new Error(
                          data.error?.message || `HTTP error! Status: ${response.status}`
                        );
                      });
                    }
                    return response.json();
                  })
                  .then((data) => {
                    if (data.error) {
                      throw new Error(data.error.message || "Failed to recheck keys");
                    }
                    this.message = `Successfully rechecked ${data.amount} keys`;
                    this.fetchKeys();
                  })
                  .catch((err) => {
                    this.error = "Failed to recheck keys: " + err.message;
                    this.loading = false;
                  });
              }
            },
          }));
        });
      </script>

      <%- include("partials/admin-footer") %>
    </div>
  </body>
</html>
