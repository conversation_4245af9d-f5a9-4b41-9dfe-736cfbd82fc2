<!DOCTYPE html>
<html lang="en">
  <head>
    <%- include("partials/admin_shared_header", { title: "View User" }) %>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </head>

  <body class="bg-background font-mono text-white">
    <div x-data="userView" class="mx-auto flex min-h-svh w-full max-w-7xl flex-col gap-4 p-4">
      <h1 class="mt-2 mb-6 text-center text-3xl font-bold">User Details</h1>

      <!-- Loading state -->
      <div class="flex flex-col items-center justify-center gap-2 p-4" x-show="loading">
        <div
          class="border-accent h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"
        ></div>
        <p>Loading user data...</p>
      </div>

      <!-- Error message -->
      <div
        class="border-border-error bg-background-error text-color-error my-4 rounded-lg border p-4"
        x-show="error"
        x-text="error"
      ></div>

      <!-- User information -->
      <template x-if="userData && !loading">
        <div class="card p-4" id="user-info-card">
          <div class="border-border mb-6 flex items-center justify-between border-b pb-4">
            <h2 class="text-3xl font-bold">User Information</h2>

            <div class="flex items-center gap-2">
              <button
                class="secondary-button"
                @click="openEditModal('nickname', userData.nickname || '')"
              >
                <span class="inline-block text-base">✏️</span> Edit Nickname
              </button>

              <button
                class="secondary-button"
                @click="openEditModal('adminNote', userData.adminNote || '')"
              >
                <span class="inline-block text-base">📝</span> Edit Admin Note
              </button>

              <button
                class="secondary-button"
                @click="openEditModal('type', userData.type || 'free')"
              >
                <span class="inline-block text-base">🔑</span> Edit Type
              </button>
            </div>
          </div>

          <div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Token:</span>
              <code class="w-max text-sm" x-text="userData.token"></code>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Nickname:</span>
              <span x-text="userData.nickname || 'None'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Type:</span>
              <span x-text="userData.type"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Admin Note:</span>
              <span x-text="userData.adminNote || 'None'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Created:</span>
              <span x-text="formatDate(userData.createdAt)"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Last Used:</span>
              <span x-text="formatDate(userData.lastUsedAt) || 'Never'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Status:</span>
              <div>
                <span
                  x-text="userData.isDisabled ? 'Disabled' : 'Active'"
                  :class="userData.isDisabled ? 'text-danger' : 'text-success'"
                ></span>
                <span
                  x-show="userData.disabledReason"
                  x-text="` (${formatDate(userData.disabledAt)})`"
                ></span>
              </div>
            </div>

            <div class="flex flex-col gap-2" x-show="userData.isDisabled">
              <span class="text-accent font-bold">Reason:</span>
              <span x-text="userData.disabledReason || 'N/A'"></span>
            </div>

            <div class="flex flex-col gap-2">
              <span class="text-accent font-bold">Registered IPs:</span>
              <span x-text="userData.ips?.length || 0"></span>
            </div>
          </div>

          <div class="border-border flex items-center justify-end border-t pt-4">
            <template x-if="!userData.isDisabled">
              <div class="flex items-center gap-2">
                <button class="secondary-button" @click="rotateUserToken(userData.token)">
                  <span class="inline-block text-base">🔄</span> Rotate Token
                </button>

                <button class="danger-button" @click="banUser(userData.token)">
                  <span class="inline-block text-base">🚫</span> Disable User
                </button>
              </div>
            </template>

            <template x-if="userData.isDisabled">
              <div class="flex items-center gap-2">
                <button class="secondary-button" @click="rotateUserToken(userData.token)">
                  <span class="inline-block text-base">🔄</span> Rotate Token
                </button>
                <button class="primary-button" @click="unbanUser(userData.token)">
                  <span class="inline-block text-base">✅</span> Reactivate User
                </button>
                <button class="danger-button" @click="deleteUser(userData.token)">
                  <span class="inline-block text-base">🗑️</span> Delete User
                </button>
              </div>
            </template>
          </div>
        </div>
      </template>

      <!-- Usage Statistics -->
      <div class="card space-y-4 p-4" x-show="userData && !loading" id="usage-stats-card">
        <div class="w-full text-center">
          <h2 class="text-3xl font-bold">Usage Statistics</h2>
        </div>

        <div class="grid grid-cols-3 gap-4">
          <div class="card bg-background-tertiary space-y-2">
            <h3 class="text-accent text-center text-base">Total Requests</h3>

            <div class="text-center text-2xl" x-text="formatNumber(getTotalPrompts())"></div>
          </div>

          <div class="card bg-background-tertiary space-y-2">
            <h3 class="text-accent text-center text-base">Last Request</h3>

            <div class="text-center text-2xl" x-text="getLastRequestDate()"></div>
            <div
              class="text-accent-light text-center text-sm"
              x-text="getLastRequestModel() || 'N/A'"
            ></div>
          </div>

          <div class="card bg-background-tertiary space-y-2">
            <h3 class="text-accent text-center text-base">Most Used Model</h3>

            <div class="text-center text-2xl" x-text="getMostUsedModel().model || 'N/A'"></div>
            <div
              class="text-accent-light text-center text-sm"
              x-text="getMostUsedModel().count ? formatNumber(getMostUsedModel().count) + ' requests' : ''"
            ></div>
          </div>
        </div>

        <!-- Token Breakdown -->
        <div class="card bg-background-tertiary flex w-full items-center justify-center gap-4">
          <div class="breakdown-item">
            <span class="text-accent">Input Tokens:</span>
            <span class="breakdown-value" x-text="formatNumber(getTotalInputTokens())"></span>
            <span class="cost" x-text="formatCost(getInputCost())"></span>
          </div>

          <div class="breakdown-item">
            <span class="text-accent">Output Tokens:</span>
            <span class="breakdown-value" x-text="formatNumber(getTotalOutputTokens())"></span>
            <span class="cost" x-text="formatCost(getOutputCost())"></span>
          </div>

          <div class="breakdown-item">
            <span class="text-accent">Total Tokens:</span>
            <span class="breakdown-value" x-text="formatNumber(getTotalTokens())"></span>
            <span class="cost" x-text="formatCost(getTotalCost())"></span>
          </div>
        </div>

        <!-- Usage Chart -->
        <div class="card bg-background-tertiary h-[500px] w-full">
          <canvas id="usageChart"></canvas>
        </div>

        <!-- Model Usage Breakdown -->
        <div
          class="mb-0 grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4"
          x-show="getModelUsageData().length > 0"
        >
          <template x-for="(model, index) in getModelUsageData().slice(0, 8)" :key="model.name">
            <div class="card bg-background-tertiary relative flex flex-col gap-2 p-3">
              <code class="w-max text-xs" x-text="model.name"></code>
              <div class="absolute top-3 right-3 text-sm" x-text="'#' + (index + 1)"></div>

              <div class="flex items-center justify-between gap-2">
                <div class="text-sm" x-text="formatNumber(model.count) + ' requests'"></div>
                <div class="text-accent text-sm" x-text="model.percentage + '%'"></div>
              </div>

              <div class="border-border h-2 w-full overflow-hidden rounded-lg border">
                <div class="bg-accent h-full" :style="'width: ' + model.percentage + '%'"></div>
              </div>
            </div>
          </template>
        </div>

        <div
          class="card bg-background-tertiary mb-0 text-center text-lg"
          x-show="getModelUsageData().length === 0"
        >
          No model usage data available
        </div>
      </div>

      <!-- Recent Requests -->
      <div
        class="card space-y-4 p-4"
        x-show="userData && !loading && userLogs.length > 0"
        id="logs-card"
      >
        <div class="w-full text-center">
          <h2 class="text-3xl font-bold">Recent Requests</h2>
        </div>

        <div class="table-responsive">
          <table
            id="logs-table"
            class="card h-px w-full border-separate border-spacing-0 overflow-hidden p-0"
          >
            <thead class="bg-background-secondary">
              <tr>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  No.
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Model
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  IP
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Input
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Output
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Total
                </th>
                <th
                  class="text-accent border-border border-b p-2 text-left font-bold tracking-wider uppercase"
                >
                  Date
                </th>
              </tr>
            </thead>
            <tbody>
              <template x-for="(log, index) in paginatedLogs" :key="index">
                <tr
                  class="*:border-border transition-colors *:border-b hover:bg-[rgba(255,255,255,0.1)]"
                  :class="{ 'bg-background-tertiary': index % 2 === 1, '*:border-none': index === paginatedLogs.length - 1 }"
                >
                  <th
                    class="text-accent p-2 px-3"
                    x-text="(logsCurrentPage - 1) * logsPerPage + index + 1"
                  ></th>

                  <td class="p-2 px-3 text-sm">
                    <code x-text="log.payload.model"></code>
                  </td>

                  <td class="p-2 px-3">
                    <code x-text="formatIp(log.payload.ip)"></code>
                  </td>

                  <td class="p-2 px-3">
                    <div
                      x-bind:title="`$${getPricePerMillion(log.input.cost, log.input.tokens)}/1M tokens`"
                    >
                      <span x-text="formatNumber(log.input.tokens)"></span>
                      <span class="cost" x-text="formatCost(log.input.cost)"></span>
                    </div>
                  </td>
                  <td class="p-2 px-3">
                    <div
                      x-bind:title="`$${getPricePerMillion(log.output.cost, log.output.tokens)}/1M tokens`"
                    >
                      <span x-text="formatNumber(log.output.tokens)"></span>
                      <span class="cost" x-text="formatCost(log.output.cost)"></span>
                    </div>
                  </td>
                  <td class="p-2 px-3">
                    <div>
                      <span x-text="formatNumber(log.input.tokens + log.output.tokens)"></span>
                      <span
                        class="cost"
                        x-text="formatCost(log.input.cost + log.output.cost)"
                      ></span>
                    </div>
                  </td>

                  <td class="p-2 px-3" x-text="formatDate(log.createdAt)"></td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <!-- Pagination controls for logs -->
        <div id="logs-pagination" class="mt-4 mb-2" x-show="userLogs.length > logsPerPage">
          <div class="flex items-center justify-center gap-4">
            <button
              class="primary-button"
              @click="changePage('prev')"
              :disabled="logsCurrentPage === 1"
              :class="{ 'cursor-not-allowed bg-[#25313c]': logsCurrentPage === 1 }"
            >
              &laquo; Prev
            </button>

            <span class="text-base">
              Page <span x-text="logsCurrentPage"></span> of
              <span x-text="totalLogsPages"></span>
            </span>

            <button
              class="primary-button"
              @click="changePage('next')"
              :disabled="logsCurrentPage === totalLogsPages"
              :class="{ 'cursor-not-allowed bg-[#25313c]': logsCurrentPage === totalLogsPages }"
            >
              Next &raquo;
            </button>
          </div>
        </div>
      </div>

      <!-- Edit Modal -->
      <div
        class="fixed top-0 left-0 z-[1000] h-full w-full items-center justify-center bg-[rgba(0,0,0,0.7)]"
        :class="{ 'flex': showModal && userData, 'hidden': !showModal || !userData }"
      >
        <div
          class="bg-background border-background-secondary w-[90%] max-w-[500px] rounded-lg border p-6 shadow-lg"
          x-show="userData"
        >
          <div class="mb-4 flex items-center justify-between">
            <h3
              class="text-lg font-bold"
              x-text="'Edit ' + (editField === 'nickname' ? 'Nickname' : editField === 'type' ? 'User Type' : 'Admin Note')"
            ></h3>
            <button
              class="cursor-pointer border-none bg-transparent text-lg text-white"
              @click="showModal = false"
            >
              &times;
            </button>
          </div>

          <div class="mb-6">
            <template x-if="editField !== 'type'">
              <div class="flex flex-col gap-2">
                <label
                  for="editValue"
                  x-text="editField === 'nickname' ? 'Nickname' : 'Admin Note'"
                ></label>

                <input
                  type="text"
                  id="editValue"
                  x-model="editValue"
                  class="border-border w-full rounded-lg border px-4 py-2"
                  :placeholder="'Enter ' + (editField === 'nickname' ? 'nickname' : 'admin note')"
                />
              </div>
            </template>

            <template x-if="editField === 'type'">
              <div class="flex flex-col gap-2">
                <label for="editValue">User Type</label>
                <div class="form-help">User type determines access levels and quotas.</div>

                <select
                  id="editValue"
                  x-model="editValue"
                  class="border-border bg-background w-full appearance-none rounded-lg border px-4 py-2"
                >
                  <option value="normal">Normal</option>
                  <option value="special">Special</option>
                </select>
              </div>
            </template>
          </div>

          <div class="flex justify-end gap-4">
            <button class="secondary-button" @click="showModal = false">
              <span>❌</span> Cancel
            </button>
            <button class="primary-button" @click="saveChanges()"><span>💾</span> Save</button>
          </div>
        </div>
      </div>

      <!-- Floating Refresh Button -->
      <div
        x-show="userData"
        class="secondary-button fixed right-4 bottom-4 flex size-13 items-center justify-center rounded-full text-lg"
        :class="{ 'refreshing': isRefreshing }"
        @click="refreshData()"
        title="Refresh data"
      >
        <span>🔄</span>
      </div>

      <!-- prettier-ignore -->
      <%- include("partials/admin-footer", { url: "/admin/manage/list-users", text: "Back to Users" }) %>
    </div>

    <script>
      document.addEventListener("alpine:init", () => {
        Alpine.data("userView", () => ({
          token: "<%= params.token %>",
          loading: true,
          error: null,
          userData: null,
          userLogs: [],
          usageChart: null,
          showModal: false,
          editField: "",
          editValue: "",
          isRefreshing: false,

          // Pagination for logs
          logsCurrentPage: 1,
          logsPerPage: 10,

          dateFormat: new Intl.DateTimeFormat("en-US", { dateStyle: "medium", timeStyle: "short" }),

          init() {
            if (this.token) {
              this.fetchUserData();
              this.fetchUserLogs();
            }
          },

          getPricePerMillion(cost, tokens) {
            return (cost / tokens) * 1_000_000;
          },

          formatIp(ip) {
            if (ip.startsWith("ip-")) return ip.slice(0, 13);
            return ip;
          },

          formatCost(cost) {
            if (cost === undefined || cost === null) return "$0.000000";
            return "$" + cost.toFixed(6);
          },

          fetchUserData(showLoading = true) {
            this.loading = showLoading;
            this.error = null;

            fetch(`/admin/api/users/${this.token}`)
              .then((response) => {
                if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                this.loading = false;
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch user data";
                  this.userData = null;
                } else {
                  this.userData = data.user;
                  this.error = null;
                }
              })
              .catch((err) => {
                this.loading = false;
                this.error = "Failed to fetch user data: " + err.message;
                this.userData = null;
              });
          },

          formatDate(timestamp) {
            if (!timestamp) return null;
            const date = new Date(timestamp);
            return this.dateFormat.format(date);
          },

          openEditModal(field, value) {
            this.editField = field;
            this.editValue = value;
            this.showModal = true;
          },

          saveChanges() {
            if (!this.editField || !this.userData) {
              this.showModal = false;
              return;
            }

            const updateData = {};
            updateData[this.editField] = this.editValue;

            fetch(`/admin/api/users/${this.userData.token}`, {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(updateData),
            })
              .then((response) => {
                if (!response.ok) {
                  return response.json().then((data) => {
                    throw new Error(
                      data.error?.message || `HTTP error! Status: ${response.status}`
                    );
                  });
                }
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to update user";
                } else {
                  // Update the local data
                  this.userData[this.editField] = this.editValue;
                  this.showModal = false;
                }
              })
              .catch((err) => {
                this.error = "Failed to update user: " + err.message;
              });
          },

          banUser(token) {
            if (confirm("Are you sure you want to disable this user?")) {
              const reason = prompt("Reason for disabling:");

              fetch(`/admin/api/users/${token}/deactivate`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ disabledReason: reason }),
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw new Error(
                        data.error?.message || `HTTP error! Status: ${response.status}`
                      );
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) {
                    throw new Error(data.error.message || "Failed to disable user");
                  }
                  return this.fetchUserData(false);
                })
                .catch((err) => {
                  this.error = "Failed to disable user: " + err.message;
                });
            }
          },

          unbanUser(token) {
            if (confirm("Are you sure you want to reactivate this user?")) {
              fetch(`/admin/api/users/${token}/reactivate`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw new Error(
                        data.error?.message || `HTTP error! Status: ${response.status}`
                      );
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) {
                    throw new Error(data.error.message || "Failed to reactivate user");
                  }
                  return this.fetchUserData(false);
                })
                .catch((err) => {
                  this.error = "Failed to reactivate user: " + err.message;
                });
            }
          },

          deleteUser(token) {
            if (
              confirm("Are you sure you want to DELETE this user? This action cannot be undone.")
            ) {
              fetch(`/admin/api/users/${token}/delete`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({}),
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw new Error(
                        data.error?.message || `HTTP error! Status: ${response.status}`
                      );
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) {
                    throw new Error(data.error.message || "Failed to delete user");
                  }
                  window.location.href = "/admin/manage/list-users";
                })
                .catch((err) => {
                  this.error = "Failed to delete user: " + err.message;
                });
            }
          },

          rotateUserToken(token) {
            if (!token) return;

            if (
              confirm(
                "Are you sure you want to rotate this user's token? The user will need to update their token in all applications."
              )
            ) {
              this.loading = true;

              fetch(`/admin/api/users/${token}/rotate`, {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({}),
              })
                .then((response) => {
                  if (!response.ok) {
                    return response.json().then((data) => {
                      throw Error(data.error?.message || `HTTP error! Status: ${response.status}`);
                    });
                  }
                  return response.json();
                })
                .then((data) => {
                  if (data.error) {
                    this.error = data.error.message || "Failed to rotate token";
                    this.loading = false;
                  } else {
                    // Show the new token to the user
                    alert(`Token rotated successfully. New token: ${data.newToken}`);

                    // Refresh the page to show the new token
                    window.location.href = `/admin/manage/view-user/${data.newToken}`;
                  }
                })
                .catch((err) => {
                  this.loading = false;
                  this.error = "Failed to rotate token: " + err.message;
                });
            }
          },

          fetchUserLogs() {
            fetch(`/admin/api/users/${this.token}/logs`)
              .then((response) => {
                if (!response.ok) throw Error(`HTTP error! Status: ${response.status}`);
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch user logs";
                } else {
                  this.userLogs = data.logs || [];

                  // Initialize chart after logs are loaded
                  this.$nextTick(() => this.initChart());
                }
              })
              .catch((err) => {
                this.error = "Failed to fetch user logs: " + err.message;
              });
          },

          // Group logs by date
          groupLogsByDate() {
            if (!this.userLogs || !this.userLogs.length) return {};

            const grouped = {};

            this.userLogs.forEach((log) => {
              const date = new Date(log.createdAt);
              const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD format

              if (!grouped[dateStr]) {
                grouped[dateStr] = {
                  count: 0,
                  inputTokens: 0,
                  outputTokens: 0,
                  inputCost: 0,
                  outputCost: 0,
                  totalCost: 0,
                };
              }

              grouped[dateStr].count++;
              grouped[dateStr].inputTokens += log.input.tokens || 0;
              grouped[dateStr].outputTokens += log.output.tokens || 0;
              grouped[dateStr].inputCost += log.input.cost || 0;
              grouped[dateStr].outputCost += log.output.cost || 0;
              grouped[dateStr].totalCost += (log.input.cost || 0) + (log.output.cost || 0);
            });

            return grouped;
          },

          initChart() {
            if (this.usageChart) {
              this.usageChart.destroy();
            }

            if (!this.userLogs || !this.userLogs.length) return;

            const groupedData = this.groupLogsByDate();
            const dates = Object.keys(groupedData).sort();

            // Prepare data for the chart
            const requestCounts = dates.map((date) => groupedData[date].count);
            const inputTokens = dates.map((date) => groupedData[date].inputTokens);
            const outputTokens = dates.map((date) => groupedData[date].outputTokens);
            const totalCosts = dates.map((date) => groupedData[date].totalCost);

            // Format dates for display
            const formattedDates = dates.map((date) => {
              const [year, month, day] = date.split("-");
              return `${month}/${day}`;
            });

            const ctx = document.getElementById("usageChart");

            this.usageChart = new Chart(ctx, {
              type: "bar",
              data: {
                labels: formattedDates,
                datasets: [
                  {
                    label: "Requests",
                    data: requestCounts,
                    backgroundColor: "rgba(54, 162, 235, 0.5)",
                    borderColor: "rgba(54, 162, 235, 1)",
                    borderWidth: 1,
                    yAxisID: "y",
                  },
                  {
                    label: "Input Tokens",
                    data: inputTokens,
                    backgroundColor: "rgba(75, 192, 192, 0.5)",
                    borderColor: "rgba(75, 192, 192, 1)",
                    borderWidth: 1,
                    yAxisID: "y1",
                  },
                  {
                    label: "Output Tokens",
                    data: outputTokens,
                    backgroundColor: "rgba(153, 102, 255, 0.5)",
                    borderColor: "rgba(153, 102, 255, 1)",
                    borderWidth: 1,
                    yAxisID: "y1",
                  },
                  {
                    label: "Total Cost ($)",
                    data: totalCosts,
                    backgroundColor: "rgba(255, 159, 64, 0.5)",
                    borderColor: "rgba(255, 159, 64, 1)",
                    borderWidth: 1,
                    yAxisID: "y2",
                    type: "line",
                    fill: false,
                    tension: 0.4,
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    type: "linear",
                    display: true,
                    position: "left",
                    title: {
                      display: true,
                      text: "Requests",
                    },
                    beginAtZero: true,
                  },
                  y1: {
                    type: "linear",
                    display: true,
                    position: "right",
                    title: {
                      display: true,
                      text: "Tokens",
                    },
                    beginAtZero: true,
                    grid: {
                      drawOnChartArea: false,
                    },
                  },
                  y2: {
                    type: "linear",
                    display: true,
                    position: "right",
                    title: {
                      display: true,
                      text: "Cost ($)",
                    },
                    beginAtZero: true,
                    grid: {
                      drawOnChartArea: false,
                    },
                  },
                },
                plugins: {
                  tooltip: {
                    callbacks: {
                      label: function (context) {
                        let label = context.dataset.label || "";
                        if (label) {
                          label += ": ";
                        }
                        if (context.parsed.y !== null) {
                          if (label.includes("Cost")) {
                            label += "$" + context.parsed.y.toFixed(6);
                          } else {
                            label += new Intl.NumberFormat().format(context.parsed.y);
                          }
                        }
                        return label;
                      },
                    },
                  },
                },
              },
            });
          },

          // Computed properties for pagination
          get paginatedLogs() {
            if (!this.userLogs || !this.userLogs.length) return [];
            const startIndex = (this.logsCurrentPage - 1) * this.logsPerPage;
            const endIndex = startIndex + this.logsPerPage;
            return this.userLogs.slice(startIndex, endIndex);
          },

          get totalLogsPages() {
            if (!this.userLogs || !this.userLogs.length) return 1;
            return Math.ceil(this.userLogs.length / this.logsPerPage);
          },

          // Method to change page and scroll to pagination controls
          changePage(direction) {
            if (direction === "prev") {
              this.logsCurrentPage = Math.max(1, this.logsCurrentPage - 1);
            } else if (direction === "next") {
              this.logsCurrentPage = Math.min(this.totalLogsPages, this.logsCurrentPage + 1);
            }

            // Scroll to the pagination container after a short delay to allow DOM update
            this.$nextTick(() => {
              const paginationElement = document.getElementById("logs-pagination");
              if (paginationElement) {
                // Scroll with smooth behavior
                paginationElement.scrollIntoView({ behavior: "smooth", block: "center" });
              }
            });
          },

          // Statistics calculation methods
          getTotalPrompts() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.length;
          },

          getTotalTokens() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.tokens || 0) + (log.output.tokens || 0);
            }, 0);
          },

          getTotalInputTokens() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.tokens || 0);
            }, 0);
          },

          getTotalOutputTokens() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.output.tokens || 0);
            }, 0);
          },

          getTotalCost() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.cost || 0) + (log.output.cost || 0);
            }, 0);
          },

          getInputCost() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.input.cost || 0);
            }, 0);
          },

          getOutputCost() {
            if (!this.userLogs || !this.userLogs.length) return 0;
            return this.userLogs.reduce((total, log) => {
              return total + (log.output.cost || 0);
            }, 0);
          },

          getLastRequestDate() {
            if (!this.userLogs || !this.userLogs.length) return "Never";

            // Sort logs by timestamp (descending)
            const sortedLogs = [...this.userLogs].sort((a, b) => {
              return new Date(b.createdAt) - new Date(a.createdAt);
            });

            // Return formatted date of the most recent log
            return this.formatDate(sortedLogs[0].createdAt);
          },

          getLastRequestModel() {
            if (!this.userLogs || !this.userLogs.length) return null;

            // Sort logs by timestamp (descending)
            const sortedLogs = [...this.userLogs].sort((a, b) => {
              return new Date(b.createdAt) - new Date(a.createdAt);
            });

            // Return model of the most recent log
            return sortedLogs[0].payload.model;
          },

          getMostUsedModel() {
            if (!this.userLogs || !this.userLogs.length) return { model: null, count: 0 };

            // Get model counts
            const modelCounts = this.getModelCounts();

            // Find the model with the highest count
            let mostUsedModel = null;
            let highestCount = 0;

            Object.entries(modelCounts).forEach(([model, count]) => {
              if (count > highestCount) {
                mostUsedModel = model;
                highestCount = count;
              }
            });

            return { model: mostUsedModel, count: highestCount };
          },

          getModelCounts() {
            if (!this.userLogs || !this.userLogs.length) return {};

            // Count occurrences of each model
            const modelCounts = {};
            this.userLogs.forEach((log) => {
              const model = log.payload.model;
              if (!modelCounts[model]) {
                modelCounts[model] = 0;
              }
              modelCounts[model]++;
            });

            return modelCounts;
          },

          getModelUsageData() {
            if (!this.userLogs || !this.userLogs.length) return [];

            // Get model counts
            const modelCounts = this.getModelCounts();
            const totalRequests = this.getTotalPrompts();

            // Convert to array and calculate percentages
            const modelData = Object.entries(modelCounts).map(([name, count]) => {
              const percentage = Math.round((count / totalRequests) * 100);
              return { name, count, percentage };
            });

            // Sort by count (descending)
            return modelData.sort((a, b) => b.count - a.count);
          },

          formatNumber(num) {
            return num.toLocaleString();
          },

          refreshData() {
            // Prevent multiple refreshes
            if (this.isRefreshing) return;

            this.isRefreshing = true;
            this.error = null;

            // First refresh user data
            fetch(`/admin/api/users/${this.token}`)
              .then((response) => {
                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch user data";
                  throw new Error(this.error);
                } else {
                  this.userData = data.user;

                  // Then refresh logs
                  return fetch(`/admin/api/users/${this.token}/logs`);
                }
              })
              .then((response) => {
                if (!response.ok) {
                  throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
              })
              .then((data) => {
                if (data.error) {
                  this.error = data.error.message || "Failed to fetch user logs";
                } else {
                  this.userLogs = data.logs || [];

                  // Reset to first page
                  this.logsCurrentPage = 1;

                  // Reinitialize chart
                  this.$nextTick(() => {
                    this.initChart();
                  });

                  // Show success message
                  const originalTitle = document.title;
                  document.title = "✓ Data refreshed!";
                  setTimeout(() => {
                    document.title = originalTitle;
                  }, 2000);
                }
              })
              .catch((err) => {
                this.error = "Failed to refresh data: " + err.message;
              })
              .finally(() => {
                this.isRefreshing = false;
              });
          },
        }));
      });
    </script>
  </body>
</html>
